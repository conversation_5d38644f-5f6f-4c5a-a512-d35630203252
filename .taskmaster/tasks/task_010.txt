# Task ID: 10
# Title: Implement Contextual Menu System
# Status: done
# Dependencies: 6, 7, 8
# Priority: medium
# Description: Create a right-click/long-press contextual menu for verse interactions with quick access to features.
# Details:
1. Create ContextualMenuController using Riverpod:
   - Track selected verse
   - Manage menu visibility
2. Implement platform-specific triggers:
   - Long-press for mobile
   - Right-click for web/desktop
3. Create menu UI with options:
   - View translations
   - View tafsir
   - Play audio
   - Copy verse
   - Share verse
   - Add bookmark
   - Add note
   - Request scholar review
4. Implement action handlers for each menu option
5. Add animation for menu appearance/disappearance

# Test Strategy:
Test contextual menu on different platforms (mobile, web, desktop). Verify all menu options work correctly. Test menu positioning relative to selected verse. Verify menu closes appropriately when clicking outside.
