# Task ID: 26
# Title: Implement Web-Compatible Database Abstraction Layer
# Status: done
# Dependencies: 3
# Priority: high
# Description: Create a platform-specific database abstraction layer that uses Supabase for web and SQLite/Drift for mobile/desktop platforms, ensuring all database operations work consistently across platforms. Address the identified drift/sqlite3 import chain causing web compilation errors.
# Details:
1. Create a database abstraction interface in `lib/core/data/database/`:
   - Define a common interface (`DatabaseInterface`) with methods for all required database operations
   - Ensure the interface is platform-agnostic and covers all current use cases

2. Implement platform-specific database providers:
   - Create `SupabaseDatabaseProvider` that implements the interface using Supabase client
   - Create `SqliteDatabaseProvider` that implements the interface using SQLite/Drift
   - Use conditional imports with `dart:io` availability checks to determine platform

3. Create a factory class for database provider instantiation:
   ```dart
   abstract class DatabaseProviderFactory {
     static DatabaseInterface getProvider() {
       if (kIsWeb) {
         return SupabaseDatabaseProvider();
       } else {
         return SqliteDatabaseProvider();
       }
     }
   }
   ```

4. Fix identified drift/sqlite3 import issues:
   - ✅ Refactor `/lib/main.dart` to avoid direct import of `drift/drift.dart` for driftRuntimeOptions
   - ✅ Fix `/lib/database/bookmark_db/database_native.dart` to use conditional imports
   - ✅ Fix `/lib/presentation/screens/books/data/data_sources/database_native.dart` to use conditional imports
   - ✅ Fix `/lib/presentation/screens/quran_page/widgets/khatmah/data/data_source/database_native.dart` to use conditional imports
   - ✅ Ensure all drift imports are properly isolated in platform-specific files

5. Implement web-compatible alternatives:
   - ✅ Create web implementations using GetStorage instead of drift for key databases
   - ✅ Implement BookmarkDatabase web version (bookmark_database_web_full.dart)
   - ✅ Implement BooksBookmarkDatabase web version (books_bookmark_database_web.dart)
   - ✅ Implement DbBookmarkHelper web version (db_bookmark_helper_web.dart)
   - ✅ Create platform-independent model classes (bookmark_model.dart, bookmark_ayah_model.dart, dheker_model_plain.dart)
   - ✅ Implement KhatmahDatabase web version using GetStorage
   - ✅ Create web-compatible khatmah_models.dart with plain Dart models
   - ✅ Add AdhkarData and companion classes to database_web.dart

6. Refactor quran_library_fork to fix web compatibility:
   - ✅ Remove direct drift import from `/lib/quran.dart` (line 11 imports `drift/drift.dart`)
   - ✅ Create platform-agnostic interface for TafsirDatabase operations in `tafsir_database_interface.dart`
   - ✅ Implement conditional imports for TafsirDatabase using factory pattern
   - ✅ Create platform-specific implementations:
     * `tafsir_database_native.dart` - Uses drift for mobile/desktop
     * `tafsir_database_web.dart` - Placeholder for web implementation
   - ✅ Move TafsirTable definition to platform-specific files with conditional imports
   - ✅ Handle drift runtime configuration using platform-specific configuration files
   - ✅ Maintain backward compatibility by using type aliases and wrapper classes
   - ✅ Leverage existing conditional imports via `database_connection.dart` that already loads platform-specific implementations
   - ✅ Ensure the part/part of pattern is maintained while removing direct drift dependencies

7. Implement web functionality for TafsirDatabase:
   - Complete the implementation of `tafsir_database_web.dart` to fetch data from Supabase
   - Create a Supabase table matching the TafsirTable schema (columns: index, sura, aya, Text, PageNum)
   - Implement methods to query tafsir data from Supabase that match the native implementation
   - Ensure data models (TafsirData/TafsirTableData) are compatible with both implementations

8. Address identified blockers:
   - ✅ Resolve type conflicts between drift-generated models and plain models
   - ✅ Refactor complex interdependencies throughout the codebase
   - ✅ Remove all drift.Value usage from companion objects
   - ✅ Fix nullable string assignments in widget files before web compilation can succeed
   - Implement repository pattern to abstract controllers from direct drift dependencies
   - Create adapter classes to bridge between drift-generated models and plain models

9. Implement comprehensive repository pattern:
   - Create repository interfaces for each data domain (bookmarks, tafsir, etc.)
   - Implement platform-specific repository implementations
   - Refactor controllers to depend on repositories instead of direct database access
   - Use dependency injection to provide appropriate repository implementations

10. Handle other data storage types:
    - ✅ Bookmarks: Continue using GetStorage for cross-platform compatibility
    - ✅ User preferences: Continue using GetStorage for cross-platform compatibility
    - Quran data: Continue loading from JSON assets
    - Implement web-compatible solution for tafsir database downloads

11. Migrate existing database operations:
    - ✅ Identify all current SQLite/Drift database operations in the codebase
    - ✅ Refactor these operations to use the abstraction layer instead of direct SQLite/Drift calls
    - ✅ Ensure data models are compatible with both Supabase and SQLite storage

12. Update dependency injection:
    - Modify any dependency injection code to use the new abstraction layer
    - Ensure services receive the appropriate database provider based on platform

13. Handle platform-specific edge cases:
    - ✅ Address any platform-specific limitations or features
    - ✅ Implement workarounds for web-specific constraints (e.g., storage limits)
    - ✅ Ensure proper error handling for platform-specific database errors

14. Centralized model definitions:
    - ✅ Create centralized model definitions to avoid import conflicts
    - ✅ Ensure BookmarkAyah model includes all necessary fields
    - ✅ Add companion classes to model files for consistent data handling

# Test Strategy:
1. Unit Testing:
   - Write unit tests for the database abstraction interface
   - Create mock implementations of the interface for testing
   - Test each database operation in isolation
   - Verify correct behavior of the factory class on different platforms

2. Integration Testing:
   - Test the Supabase implementation against a test Supabase instance
   - Test the SQLite implementation against a test database
   - Verify data consistency between implementations
   - Test all CRUD operations on both implementations

3. Platform-Specific Testing:
   - Run tests on web platform to verify Supabase implementation works correctly
   - Run tests on mobile/desktop platforms to verify SQLite implementation works correctly
   - Verify correct provider selection based on platform
   - Specifically test web compilation to ensure no native drift/sqlite imports are leaking through
   - Verify quran_library dependency works correctly on web after refactoring

4. TafsirDatabase Testing:
   - Test the web implementation of TafsirDatabase with Supabase
   - Verify TafsirTable data is correctly retrieved from Supabase on web
   - Compare results between native and web implementations to ensure consistency
   - Test performance with large tafsir datasets

5. Data Storage Testing:
   - Verify GetStorage works correctly for bookmarks and preferences on all platforms
   - Test JSON asset loading for Quran data on all platforms
   - Test downloaded content functionality on both web and native platforms

6. Migration Testing:
   - Test existing features that previously used SQLite directly
   - Verify all functionality works identically after migration
   - Check for any performance regressions

7. Edge Case Testing:
   - Test offline behavior on mobile/desktop
   - Test with large datasets to verify performance
   - Test synchronization between platforms if implemented
   - Verify proper error handling for network issues on web

8. Manual Testing:
   - Perform manual testing of key database operations on all platforms
   - Verify UI components display data correctly regardless of platform
   - Test the application's behavior when switching between online and offline states
   - Verify web build compiles without any native dependency errors
   - Test nullable string assignments in widget files after fixes are implemented

9. Repository Pattern Testing:
   - Test repository interfaces with mock implementations
   - Verify controllers work correctly with repository abstractions
   - Test adapter classes that bridge between drift models and plain models
   - Ensure consistent behavior across different repository implementations

10. Web Compilation Testing:
    - Verify the app compiles successfully for web platforms
    - Test the compiled web app in different browsers
    - Verify all database operations work correctly in the web environment
    - Check for any performance issues specific to web platforms

# Subtasks:
## 26.1. Fix drift/sqlite3 import chain in main.dart [done]
### Dependencies: None
### Description: Refactor main.dart to avoid direct import of drift/drift.dart for driftRuntimeOptions. Use conditional imports or move this configuration to a platform-specific file.
### Details:


## 26.2. Fix drift imports in bookmark_db [done]
### Dependencies: None
### Description: Refactor /lib/database/bookmark_db/database_native.dart to properly use conditional imports and ensure drift/native.dart is not imported in web contexts.
### Details:


## 26.3. Fix drift imports in books data sources [done]
### Dependencies: None
### Description: Refactor /lib/presentation/screens/books/data/data_sources/database_native.dart to properly use conditional imports and ensure drift/native.dart is not imported in web contexts.
### Details:


## 26.4. Fix drift imports in khatmah data sources [done]
### Dependencies: None
### Description: Refactor /lib/presentation/screens/quran_page/widgets/khatmah/data/data_source/database_native.dart to properly use conditional imports and ensure drift/native.dart is not imported in web contexts.
### Details:


## 26.5. Refactor quran_library_fork to remove direct drift imports [done]
### Dependencies: None
### Description: Modify quran_library_fork/lib/quran.dart to remove the direct import of drift/drift.dart. Move drift-specific code to platform-specific files and use conditional imports.
### Details:


## 26.6. Create database abstraction interface [done]
### Dependencies: None
### Description: Define a common DatabaseInterface with methods for all required database operations in lib/core/data/database/.
### Details:


## 26.7. Implement platform-specific database providers [done]
### Dependencies: None
### Description: Create SupabaseDatabaseProvider and SqliteDatabaseProvider implementations of the interface.
### Details:


## 26.8. Create database provider factory [done]
### Dependencies: None
### Description: Implement a factory class that returns the appropriate database provider based on platform.
### Details:


## 26.9. Verify web compilation after import fixes [done]
### Dependencies: None
### Description: Test web compilation to ensure all native drift/sqlite imports have been properly isolated and the application builds for web without errors.
### Details:


## 26.10. Create Supabase migration for necessary tables [done]
### Dependencies: None
### Description: Implement migration scripts to create all necessary tables in Supabase that match the schema used in SQLite/Drift.
### Details:


## 26.11. Refactor TafsirDatabase in quran_library_fork [done]
### Dependencies: None
### Description: Refactor the TafsirDatabase class in quran_library_fork to work without direct drift dependency on web. Move drift.DriftDatabase annotation and related code to platform-specific files.
### Details:


## 26.12. Test database abstraction with real data [done]
### Dependencies: None
### Description: Verify that both Supabase and SQLite implementations correctly handle real application data and maintain consistency across platforms.
### Details:


## 26.13. Leverage existing conditional imports in quran_library_fork [done]
### Dependencies: None
### Description: Utilize the existing conditional imports via database_connection.dart that already loads platform-specific implementations (database_native.dart for mobile, database_web.dart for web).
### Details:


## 26.14. Maintain part/part of pattern in quran_library_fork [done]
### Dependencies: None
### Description: Ensure the part/part of pattern in quran_library_fork is maintained while removing direct drift dependencies from the main quran.dart file.
### Details:


## 26.15. Implement TafsirDatabase web functionality [done]
### Dependencies: None
### Description: Complete the implementation of tafsir_database_web.dart to provide actual functionality for web platforms, replacing the current placeholder implementation.
### Details:


## 26.16. Test TafsirDatabase platform-specific implementations [done]
### Dependencies: None
### Description: Verify that the platform-specific implementations (tafsir_database_native.dart and tafsir_database_web.dart) are correctly loaded based on platform and function as expected.
### Details:


## 26.17. Create Supabase table for TafsirTable data [done]
### Dependencies: None
### Description: Create a Supabase table that matches the TafsirTable schema with columns: index, sura, aya, Text, and PageNum to store tafsir data for web access.
### Details:


## 26.18. Implement Supabase queries for tafsir data [done]
### Dependencies: None
### Description: Develop Supabase queries in tafsir_database_web.dart that match the functionality of the native implementation, ensuring consistent data retrieval across platforms.
### Details:


## 26.19. Implement web-compatible solution for tafsir database downloads [done]
### Dependencies: None
### Description: Create a web-compatible solution for downloading and storing tafsir databases that currently use GitHub downloads in the native implementation.
### Details:


## 26.20. Verify GetStorage compatibility across platforms [done]
### Dependencies: None
### Description: Test and ensure that GetStorage works correctly for bookmarks and user preferences on all platforms, including web.
### Details:


## 26.21. Implement BookmarkDatabase web version [done]
### Dependencies: None
### Description: Create and test the web implementation of BookmarkDatabase (bookmark_database_web_full.dart) using GetStorage instead of drift.
### Details:


## 26.22. Implement BooksBookmarkDatabase web version [done]
### Dependencies: None
### Description: Create and test the web implementation of BooksBookmarkDatabase (books_bookmark_database_web.dart) using GetStorage instead of drift.
### Details:


## 26.23. Implement DbBookmarkHelper web version [done]
### Dependencies: None
### Description: Create and test the web implementation of DbBookmarkHelper (db_bookmark_helper_web.dart) using GetStorage instead of drift.
### Details:


## 26.24. Create platform-independent model classes [done]
### Dependencies: None
### Description: Develop platform-independent model classes (bookmark_model.dart, bookmark_ayah_model.dart, dheker_model_plain.dart) that can be used across both web and native implementations.
### Details:


## 26.25. Resolve type conflicts between drift models and plain models [done]
### Dependencies: None
### Description: Create adapter classes or conversion methods to resolve type conflicts between drift-generated models and plain model classes to ensure compatibility across platforms.
### Details:


## 26.26. Implement repository pattern for database access [done]
### Dependencies: None
### Description: Create repository interfaces and implementations to abstract database access from controllers, allowing for platform-specific implementations without changing controller code.
### Details:


## 26.27. Refactor controllers to use repository pattern [done]
### Dependencies: None
### Description: Update controllers that currently import drift directly to instead use the repository interfaces, removing direct dependencies on drift throughout the codebase.
### Details:


## 26.28. Implement dependency injection for repositories [done]
### Dependencies: None
### Description: Set up dependency injection to provide the appropriate repository implementation based on platform, ensuring controllers receive the correct implementation without platform-specific code.
### Details:


## 26.29. Implement KhatmahDatabase web version [done]
### Dependencies: None
### Description: Create and test the web implementation of KhatmahDatabase using GetStorage instead of drift for web compatibility.
### Details:


## 26.30. Create web-compatible khatmah models [done]
### Dependencies: None
### Description: Develop plain Dart model classes for khatmah functionality that can be used across both web and native implementations.
### Details:


## 26.31. Add AdhkarData and companion classes to database_web.dart [done]
### Dependencies: None
### Description: Implement AdhkarData and related companion classes in database_web.dart to ensure web compatibility for adhkar functionality.
### Details:


## 26.32. Remove drift.Value usage from companion objects [done]
### Dependencies: None
### Description: Refactor all companion objects to remove drift.Value usage, ensuring web compatibility across the codebase.
### Details:


## 26.33. Fix nullable string assignments in widget files [done]
### Dependencies: None
### Description: Identify and fix all nullable string assignments in widget files that are causing web compilation errors.
### Details:


## 26.34. Test web app in different browsers [done]
### Dependencies: None
### Description: Test the compiled web application in different browsers (Chrome, Firefox, Safari, Edge) to ensure consistent behavior across all platforms.
### Details:


## 26.35. Optimize web performance [done]
### Dependencies: None
### Description: Identify and address any performance issues specific to the web platform, particularly around database operations and UI rendering.
### Details:


## 26.36. Document web-specific implementation details [done]
### Dependencies: None
### Description: Create comprehensive documentation of the web-specific implementation details, including the database abstraction layer, model adaptations, and platform-specific code organization.
### Details:


