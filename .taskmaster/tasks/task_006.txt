# Task ID: 6
# Title: Implement Quran Reading UI - Basic Layout
# Status: done
# Dependencies: 2, 5
# Priority: high
# Description: Create the core Quran reading interface with mushaf format display and basic navigation.
# Details:
1. Create QuranPage widget with:
   - Responsive layout for different screen sizes
   - Support for both portrait and landscape orientations
   - Proper text rendering with Arabic font
   - Verse numbering and surah headers
   - Waq<PERSON> (pause) marks display
2. Implement page navigation:
   - Swipe gestures for page turning
   - Page number indicator
   - Jump to page functionality
3. Create Surah index screen:
   - Searchable list of surahs
   - Display metadata (revelation type, verses count)
   - Quick navigation to specific surah
4. Implement zoom functionality:
   - Pinch to zoom gesture
   - Double-tap to zoom
   - Reset zoom level option

# Test Strategy:
Test UI rendering on different screen sizes and orientations. Verify Arabic text displays correctly with proper RTL formatting. Test navigation between pages and surahs. Verify zoom functionality works as expected. Test on both mobile and web platforms.
