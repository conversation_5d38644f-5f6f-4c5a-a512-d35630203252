# Task ID: 2
# Title: Implement Theming and Localization
# Status: done
# Dependencies: 1
# Priority: high
# Description: Set up theming system with light/dark mode support and implement localization for Arabic (RTL) and English languages.
# Details:
1. Create theme configuration in lib/core/theme/:
   - Define color schemes for light and dark modes
   - Create typography styles based on platform guidelines
   - Implement ThemeData for both modes
2. Set up localization:
   - Create lib/core/localization/ directory
   - Add ARB files for Arabic and English translations
   - Implement LocalizationsDelegate
   - Configure RTL text direction support
3. Create a theme controller using Riverpod:
   - Theme switching functionality
   - Persistence of theme preference
4. Implement Hijri calendar integration using hijri package
5. Create a language selector in settings

# Test Strategy:
Test theme switching between light and dark modes. Verify RTL layout rendering for Arabic text. Test localization by switching between languages and verifying text displays correctly. Ensure Hijri dates display properly.
