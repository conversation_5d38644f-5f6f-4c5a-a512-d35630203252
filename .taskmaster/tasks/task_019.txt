# Task ID: 19
# Title: Implement Web-Specific Features
# Status: pending
# Dependencies: 1, 2
# Priority: medium
# Description: Develop web-specific features including URL routing, responsive design, and browser optimizations, building on the successful web compatibility fixes.
# Details:
1. Implement URL routing with go_router:
   - Define route patterns for all screens
   - Handle deep linking
   - Implement history management
   - Create SEO-friendly URLs
2. Optimize for web performance:
   - Implement code splitting
   - Configure service workers
   - Set up asset preloading
   - Optimize the 6.2MB main.dart.js bundle size
3. Create responsive web layouts:
   - Desktop-specific UI adjustments
   - Keyboard shortcuts
   - Right-click menu support
4. Implement browser features:
   - Favicon and web manifest
   - Share API integration
   - PWA capabilities
5. Add web analytics integration
6. Build upon existing web compatibility fixes:
   - Ensure all new database models follow the pattern established for AdhkarCompanion, BookmarksCompanion, etc.
   - Maintain consistent type handling between web and native implementations
   - Continue using platform-appropriate companion constructors without direct drift imports in controllers
   - Verify all companion classes have proper .insert() constructors

# Test Strategy:
Test URL routing with various navigation patterns. Verify history management works correctly. Test responsive layouts on different screen sizes. Measure web performance metrics (FCP, TTI). Verify PWA installation and offline functionality. Test database operations across platforms to ensure compatibility. Verify web build continues to compile successfully with all new features.

# Subtasks:
## 19.1. Web Database Compatibility Fixes [completed]
### Dependencies: None
### Description: Fixed drift database compilation errors for web platform
### Details:
Successfully resolved type mismatches between web and native implementations for AdhkarCompanion, BookmarksCompanion, BookmarksAyahsCompanion, and KhatmahsCompanion. Updated database_web.dart to use String type for count field. Created compatible Value<T> class for web. Removed drift imports from controllers. Fixed type mismatches in bookmark models. Ensured all companion classes have .insert() constructors.

## 19.2. Verify Web Build Compilation [completed]
### Dependencies: None
### Description: Confirm successful web build compilation after database compatibility fixes
### Details:
Verified that the app now builds for web without any drift-related type mismatches. Confirmed that all database operations are properly abstracted to work across both web and native platforms. The web build generates a 6.2MB main.dart.js file successfully.

## 19.3. Implement URL Routing System [pending]
### Dependencies: None
### Description: Set up go_router for web navigation
### Details:
Configure go_router package to handle web-specific navigation. Define route patterns for all app screens. Implement deep linking functionality. Set up proper history management for browser back/forward navigation. Create SEO-friendly URL structure.

## 19.4. Optimize Web Performance [pending]
### Dependencies: None
### Description: Implement web-specific performance optimizations
### Details:
Analyze and optimize the 6.2MB main.dart.js bundle size. Implement code splitting to reduce initial load time. Configure service workers for caching and offline support. Set up asset preloading for critical resources.

