{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_DEBUG=1 DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e98c329620c51892527db69ac984ef9321b", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_PROFILE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e986eaba3bbf34fffc52894406988f981b0", "name": "Profile"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_RELEASE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e9804db47a3ceef83edd118018eb43bf272", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d0b25d39b515a574839e998df229c3cb", "path": "../Podfile", "sourceTree": "SOURCE_ROOT", "type": "file"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985026d369957ab3c55057ae76ad963dce", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/audio_service-0.18.15/ios/Classes/AudioServicePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980ec62cb0aecd706a42e6e68a0fc554ff", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/audio_service-0.18.15/ios/Classes/AudioServicePlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b4696ab7ec2c2e26fc74bf19594fd3cf", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985dcde47fabe06d61fcd3c30ec08561c8", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98acda1a17bc91bcd0373d1bc1bd574bce", "name": "audio_service", "path": "audio_service", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98607549cef4209b18934f6f8f4aefd22a", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b86489b9943f820947fed5fce222c160", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9861ebb21cac24208e7b14d3243ea693b5", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981e46cc4536c276da6fcb987e1f77273a", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9862c049e6b100edae9764d7d3551ce9e0", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980faa01741e8216e4be6c35c51876b3e1", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9869d650b2033a64700aa3fe6d1f9c1488", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e8f62b6f5d19183b8cf3ee60378f678a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983dc19c4fbe81fb73635ff0160980d1f5", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ac718ed91fdf06d5f32e5fe19e9e2062", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980b270e4e11d354aa1bb5fab6137e70e8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986bfc32e49efe08222620a6fcb0bfef9c", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/audio_service-0.18.15/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98ec39eb94ea621bf34e8cd957988fa431", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/audio_service-0.18.15/ios/audio_service.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98978d9204385156b34bc6b8de73a8b93e", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/audio_service-0.18.15/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987ef04237571ebaca7047c9916e62cdd1", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98594976fdb6d9b60f60fe1b9605c92e78", "path": "audio_service.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98438acf37b8a9dbf69c1c5dace2ec27f1", "path": "audio_service-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e980314c81d40c4b8cf1a90e84099a716c1", "path": "audio_service-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980ce7a092f44fc08120c9871d69c04d31", "path": "audio_service-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982f040f1ce175e3690734d483cf20bc28", "path": "audio_service-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980a45bd396e7d8e4237e6890a8fcabc59", "path": "audio_service.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98bbe61b907cb7de71a909bf735ec07550", "path": "audio_service.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9840c5fca37b9854b078d976e522395c9b", "name": "Support Files", "path": "../../../../Pods/Target Support Files/audio_service", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983d5e14fd2dffe882d0dcc39dc6079b9a", "name": "audio_service", "path": "../.symlinks/plugins/audio_service/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98206a2506c26df32d017c96af7d98d241", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/audio_session-0.1.21/ios/Classes/AudioSessionPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98925cd321d79bc44f6cf24334961330c7", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/audio_session-0.1.21/ios/Classes/AudioSessionPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986029abf8761e7c19b7772dcfde9b2662", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/audio_session-0.1.21/ios/Classes/DarwinAudioSession.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982195280d7f4e7ccfbec8a09bf519770a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/audio_session-0.1.21/ios/Classes/DarwinAudioSession.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9879415006ff86e5ee2e69dd60b8cd1ff9", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9805ec52bbe74c8ab644b926f12f89fb17", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984b90f599c90c47c58de9723c15c8d7d1", "name": "audio_session", "path": "audio_session", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982d3ce5c4e43b3a1c655422c6a5391c71", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c5b1eef8395b9bb603b26c22aac281ca", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984b70aed2ff0ac23fda00851f770dc38d", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984805fa6aaa180ab53c67fd4544f88b70", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983cef50ee0f030c7cc3a2432176735d56", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989997f205d38f003c39cb23d2df410e9f", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cab8ca4fc993f36148cc20a2d54b55d2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98537f9d9bd2172f9e613cc799ff009a0e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b51bfa1d970ed26e7b740589a9aea861", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982a86e94b3f8f64a32307833ce40343b1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e7433a7498dd3769f9a62bd0fc25dbb2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9827ef8c1d19413f25086041204da46644", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/audio_session-0.1.21/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9847d01650b9bec3314a994f5f929b94df", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/audio_session-0.1.21/ios/audio_session.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9862d4cfc024841c94e7b75d4d1309b48b", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/audio_session-0.1.21/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987146ffbd111b7286445586edd8817233", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9884ff81ea09223d1cd73af7d91d30fae2", "path": "audio_session.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986ce003ac21f5976273a3420538dea40f", "path": "audio_session-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98c05eb711ec070c4370b695d161b002cc", "path": "audio_session-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cd72fe3e5b6ffaff8fcd1dfaca9f6d70", "path": "audio_session-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b308a0228771ac7919981666787e28e0", "path": "audio_session-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98551ff1f0e9dc1101695f4c8cc11cc410", "path": "audio_session.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9838979cfc137e9c7d33a4bc2e05ffe723", "path": "audio_session.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9848a26e24e92d8d67197c5ecf59867193", "name": "Support Files", "path": "../../../../Pods/Target Support Files/audio_session", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981268710d77cd37bb2097fc527cfa7e9f", "name": "audio_session", "path": "../.symlinks/plugins/audio_session/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ef065035f2367bf8f380e45367a30f35", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/awesome_notifications-0.9.3+1/ios/Classes/AwesomeNotificationsPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9897d56e7251f2041d1db9cf489b18e36c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/awesome_notifications-0.9.3+1/ios/Classes/AwesomeNotificationsPlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988ca32f6a393dc77e742e4c7534ea4256", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/awesome_notifications-0.9.3+1/ios/Classes/lib/DartAwesomeNotificationsExtension.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984c32ac99c5aee5e909638cf23c966acb", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/awesome_notifications-0.9.3+1/ios/Classes/lib/DartBackgroundExecutor.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984d109b7c1d85dc75d29ca359c9f6020d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/awesome_notifications-0.9.3+1/ios/Classes/lib/FlutterAudioUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984dbc7ccbc414bae68a8f0f1db881a803", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/awesome_notifications-0.9.3+1/ios/Classes/lib/FlutterBitmapUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986eb41d3c8e226d948b75c6bb36499e02", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/awesome_notifications-0.9.3+1/ios/Classes/lib/SwiftAwesomeNotificationsPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fac64c942c3c127173d36c5b306e41b7", "name": "lib", "path": "lib", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980c5e04a7f50090532de915f023536dd3", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984d3317e89ab78998ada6484a359a1b34", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980b106fd009698d12a45fba5f4fc9cfac", "name": "awesome_notifications", "path": "awesome_notifications", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f9ad0c0b28092a6c5ae30b946462c7db", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9895be8516c2805ea7a7146092e168fedb", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a4e325f0be58ef98245b91df2b5eb083", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9844eb941b572cea4370a4d135d96b210e", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98529a25901a7f25bc7d3e1c4138a2255d", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b84cec82bb20c31f9089c7996acc005f", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ae98b5ccaa287ecfc0bd36d644ec464c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988d33131c24f5fbe346cefd2cae6f53f8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986e4b20991c059710dc8d52903ab37c5d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bef6610acb6ca29f0ac31e48cdf2f098", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d6c701d0e326742e6495704bd97c35d0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e252847572cc425e244067ffe451cd5b", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/awesome_notifications-0.9.3+1/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e987b5fdc8664f4811e759a2195546bc814", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/awesome_notifications-0.9.3+1/ios/awesome_notifications.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e987b52c6989d970da5ed4e7e94c36a097a", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/awesome_notifications-0.9.3+1/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986d682323da367afb6e995c40675e4f89", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9862ec38f526a9863effd8c73ffe6562de", "path": "awesome_notifications.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989941085a9a565e4a9f8422a75985acd0", "path": "awesome_notifications-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98fd154b0934636a3a968fbdcec9139b15", "path": "awesome_notifications-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9893d7d69c929936cb69754a339d41a669", "path": "awesome_notifications-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ed05635c3ad21efc85364d28942a83a6", "path": "awesome_notifications-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c2f51d28444b54a714306e2701087e50", "path": "awesome_notifications.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d797db29514b68a08afeab42be47b50c", "path": "awesome_notifications.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987778f49d44fa5735694a226a36c4f04f", "name": "Support Files", "path": "../../../../Pods/Target Support Files/awesome_notifications", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aca184eb8321d52bd52ab0a3cf2264d2", "name": "awesome_notifications", "path": "../.symlinks/plugins/awesome_notifications/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e3e2a94954ad4a80711725ae4ceb0a73", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/awesome_notifications_core-0.9.3/ios/Classes/AwesomeNotificationsCorePlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e0d848c461ecd007d583ccd3b573addd", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984f41be6d20bc0011eb46818f037cdc29", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98de1569e311d888b904937a7165a61bdc", "name": "awesome_notifications_core", "path": "awesome_notifications_core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989da36d9d2d6df8be5a5cd6ef28596f11", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d666780887114939d113ad765eca929e", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c3810f787a4072e34f4ebfa0667a82f3", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98acc9a230d446b01204649615a4dafeb4", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9811a9b4f43a23f8dd0f0fc5f2d9969596", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98592220848b3b7178e98bb5cd676a76de", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98211c67e793403c74f4ede55016b5057f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983e5c59a622966aaa807efacc1596df5f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987f21a1054c7df245c6ccbc99f552de1b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9824b1554fc3ba1dd0485beefff0d8bc8a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980fcd255ac020adc4aca20468e82560f4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986367dc4e157844ff520237358ca3591a", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/awesome_notifications_core-0.9.3/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e988c632918c4dd44a0bfd7d0e71230acf2", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/awesome_notifications_core-0.9.3/ios/awesome_notifications_core.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e982df1919a7fa13593aada975a0e9e1ed1", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/awesome_notifications_core-0.9.3/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98aa6693053386725ed69ae379b216ccca", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e988db85b2ad0c2f0440175f120c6ce5574", "path": "awesome_notifications_core.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98581351521dd358399cdfbf7d2eaf1588", "path": "awesome_notifications_core-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9865e414b98bb70fd12d1ea7158514bda1", "path": "awesome_notifications_core-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e98c60c2e344a034234e2f314fb846a6", "path": "awesome_notifications_core-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d811bc4ff689cffba131978fe534e08e", "path": "awesome_notifications_core-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981fcc54d1cc086261a57a433239c6704b", "path": "awesome_notifications_core.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98586d44c3d377f02c91cc8e9d8d625e01", "path": "awesome_notifications_core.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ce6f1797899740f356ab6f6fce298e8b", "name": "Support Files", "path": "../../../../Pods/Target Support Files/awesome_notifications_core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fcc955a302ed73ff2bcfc40bad231f09", "name": "awesome_notifications_core", "path": "../.symlinks/plugins/awesome_notifications_core/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983423443be3f9b29cf143522febe82273", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/background_fetch-1.3.7/ios/Classes/BackgroundFetchPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f1ad298994f79eb1941b39a23fd3ed7f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/background_fetch-1.3.7/ios/Classes/BackgroundFetchPlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9860deb82312a050f5bd21f79f392c9066", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e981bb5e2f880991a75e231962193c1720c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/background_fetch-1.3.7/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a911027301cd01cd7383ebb6fc067108", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987d39f1803bc1992467535c6940625b47", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98332a2ea1c4874bdf7225f958b139c587", "name": "background_fetch", "path": "background_fetch", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9845e91eca66c41b3384615dc1b9a61660", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984a7501f01122a7b25f0ac5fb622812ba", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98863af0a29eacf06ccd3826e9f37de97b", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ccf9b34f3795423e2f6d0d446acae5d3", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9895412742a874984aafa1d9a33a011563", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dd2d9aefd8251d8f3c44f18c4fef109d", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989f8807262762e4081d48dc8fdc71f38f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f879f8d89231aabfb1de11b34ec6bf93", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ae6a8781dcf4b89a249683683b85ae4c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981955ac3b432123159458dc45ab6a19ab", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988b9243bf343b10160420372db54af518", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98408440e9c756d2d57118ce5014b4be0f", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/background_fetch-1.3.7/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e986566498466f99bac00ba61a4deb35aea", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/background_fetch-1.3.7/ios/TSBackgroundFetch.xcframework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98468888f59990f4110d6130009174fdb1", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98afac65cc05dc1eb4395de98ea69eea2a", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/background_fetch-1.3.7/ios/background_fetch.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98de67ee7bee60915c905ee9b631f73b5f", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/background_fetch-1.3.7/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9821d41f39239117e175b186cba1964fb8", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e989877557c122b879c997d3ee2dbd770ea", "path": "background_fetch.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f42a59af34426a3d4822d6b7d7611c28", "path": "background_fetch-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98664f6719960924f48494c44f89d1e041", "path": "background_fetch-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986d01d0cdbad4854a608f88ad0e3084d2", "path": "background_fetch-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984ad2ad20ef0591adcb40676205189336", "path": "background_fetch-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e98c70bbc0c937f7cabe90c6b405d4de881", "path": "background_fetch-xcframeworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e983844a20150a7318609d7a75c8f6c6cbe", "path": "background_fetch.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e984773179ad5d5bc267a366e9c6e94eea5", "path": "background_fetch.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9848bb9370bd3c19b285b3b464a820d866", "path": "ResourceBundle-TSBackgroundFetch-background_fetch-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98aa342d710dd291fb09a0c1189c8b625d", "name": "Support Files", "path": "../../../../Pods/Target Support Files/background_fetch", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98462c91270864252bf3e54a780cd73005", "name": "background_fetch", "path": "../.symlinks/plugins/background_fetch/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b44bc222930a190e0f326bedcb897333", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.0/darwin/connectivity_plus/Sources/connectivity_plus/ConnectivityPlusPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985fa99900e317db8eaf49634f9bb3ee6c", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.0/darwin/connectivity_plus/Sources/connectivity_plus/ConnectivityProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989b7f181ac6b2ab647268094857a53934", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.0/darwin/connectivity_plus/Sources/connectivity_plus/PathMonitorConnectivityProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98f075f8c78cae8aa0f06b8d686953c997", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.0/darwin/connectivity_plus/Sources/connectivity_plus/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98da4861ceb91e11352f7a0655fff5da92", "name": "connectivity_plus", "path": "connectivity_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98adf02fc107424527f9a630f65aeed9dd", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eca5e8d80f0d8a8e215ab4068939aceb", "name": "connectivity_plus", "path": "connectivity_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98398a64edc7e64c659e861149aa3e1d1a", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989b758602d97912ee924555ee0b371920", "name": "connectivity_plus", "path": "connectivity_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9862ad29ff9534e60515a54db3bf97a559", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b041b00371eea5ddce84f80999b937a3", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c112cf756fa039a60e6f13ee7e4490ef", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b5d0280e7e4339eafe891b21a53c13da", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9816386f1b1af77c2ee0e74bd85b93d476", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e0d26ddbf586f6b4b0fbc05699353d24", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a76a5a954aca0b59c84e28f817a21078", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c719ce9000548cf63dc84bad14a4391e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980eca6e60314d5e3a6476129915d3a21f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984c3d3bc957c1fbf0086a648a512f08b2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986b50406937c1f114ec618970427edb19", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985939075b1ee5721ce4fe6df7bc8560ae", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98448696a03571ca78a183c957b318856b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987c9b92248576bbac2a06bd9c5348b9b7", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.0/darwin/connectivity_plus/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e987fbc272b451fa0059881e53576ed1eee", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.0/darwin/connectivity_plus.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98e024fa0c0ce218f1a5394220dc769cb0", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.0/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982a4ce881d80c3d1c2abd880cbb306bd1", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e981ae20b02ed466aa07408822dea3f9b1b", "path": "connectivity_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a4ad0d98da0ba09e33677927e12a857e", "path": "connectivity_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e987c02df040e91fbee4ec13e8b2cf9b56d", "path": "connectivity_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c55f833b9bc348af853d4391d1a2c3f2", "path": "connectivity_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98eb90f941091d5945c42e9bdffcd90588", "path": "connectivity_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ab2e0071db2279ad5100046b07c5a25d", "path": "connectivity_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e985746df4f4d4e82e4de98a9299b33e730", "path": "connectivity_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98775ae18662f3e29264739d2f910480ac", "path": "ResourceBundle-connectivity_plus_privacy-connectivity_plus-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989f975288ebae6e238922c4f6a4a776d3", "name": "Support Files", "path": "../../../../Pods/Target Support Files/connectivity_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981b7efe440656f29ed1b4fd2941fd4247", "name": "connectivity_plus", "path": "../.symlinks/plugins/connectivity_plus/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98e4b2cd2860dc2d7c63793b064c4e8cb3", "path": "Flutter.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983595133d29e6645c4c148a47952f1606", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9894bc66c42c0863aadd81c299dc922f59", "path": "Flutter.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e987dbe3eda22c6367ad31ab45df608862d", "path": "Flutter.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98026fee2e3318395c058d1feaf16b6416", "name": "Support Files", "path": "../Pods/Target Support Files/Flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989cc7f20a773abe8530fb1a24a75e0244", "name": "Flutter", "path": "../Flutter", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d68c2418ee454e8f53f2a6cfd9dbe2ea", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_native_splash-2.4.3/ios/flutter_native_splash/Sources/flutter_native_splash/FlutterNativeSplashPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987a426ae218cb85cc276cc08d9748b635", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_native_splash-2.4.3/ios/flutter_native_splash/Sources/flutter_native_splash/FlutterNativeSplashPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9851759c5c832d9009ed3598519f3bc160", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_native_splash-2.4.3/ios/flutter_native_splash/Sources/flutter_native_splash/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9885839ba5763fd506ff92cc767df573ad", "name": "flutter_native_splash", "path": "flutter_native_splash", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9839e2a72b67318dbeff13398fa241916e", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f2e12c9d59079b5b238920b6c01d2345", "name": "flutter_native_splash", "path": "flutter_native_splash", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9823cefb68ab572b2416ad420697e35939", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9884542f13e780c7a17245a29b24051c38", "name": "flutter_native_splash", "path": "flutter_native_splash", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9865e39ac417e41702134ac1723ddde320", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981e8a31f0eb1e95097953fec988f687b9", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988241089ba9c13a8983af8ea3dbde73c6", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98266763df6a42bb1960222703b0c9976f", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986312c879e92eba71f8be3eed4d55f57d", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fbe950174c93368d991106d3c6ef06bf", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fc657a6e61f95aa5d8af5f08bf4d1e59", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98671da4961a478dc028152d8e987c9827", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984f2f44a5bff71feb217341356346ea9a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9839516353751a3c4150cb651e52a5e40b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9854d10af345c13bcbf00817f266318018", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9831fba2ce2d01e5189866c4a91861c2e4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988a83bc221362a753758d0a50173ea704", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9845fa4ddd6adf8991cc0e81b2b65594d9", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_native_splash-2.4.3/ios/flutter_native_splash/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98fd838fb1991246d24eac02f772908440", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_native_splash-2.4.3/ios/flutter_native_splash.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98f9030e15889fffb48f39faf0653ae85d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_native_splash-2.4.3/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986fb2fc05103c876aba0c63c243b04188", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9876d57eabe12160cbfb69d523aaacb1a1", "path": "flutter_native_splash.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a4608e5e65c56d7e4acf277a19f3f91a", "path": "flutter_native_splash-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e980981987786d2557c2d64ce61863a56ef", "path": "flutter_native_splash-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f4b74d6ba913dc5bbcb4355eb3b99dbc", "path": "flutter_native_splash-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c9aa166a6d4ce194185adc96cffa7d7f", "path": "flutter_native_splash-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e984590de3df58e5f6a915a1c902faf5483", "path": "flutter_native_splash.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ad9caa546ce097ce3910fc1b1b91450b", "path": "flutter_native_splash.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98da002dbe646942e37d568bb7206c315d", "path": "ResourceBundle-flutter_native_splash_privacy-flutter_native_splash-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988b00a51a8cc16741df23d46f577d180d", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_native_splash", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a4980f5ddb63c37bad3039981e82617c", "name": "flutter_native_splash", "path": "../.symlinks/plugins/flutter_native_splash/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982af052c945cf056c6b8fa08942621141", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_timezone-3.0.1/ios/Classes/FlutterTimezonePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98406d6bf448b332b667ef85191e328f40", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_timezone-3.0.1/ios/Classes/FlutterTimezonePlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b602e688fcc34cadc9d27e2c3850159e", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984a23d726ce033e23c108d99713a83188", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98690a501a61475454059d2a4d3fbd80c8", "name": "flutter_timezone", "path": "flutter_timezone", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98448b73e1eda73ea478dd7548f0355d91", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dd525f201af43287c220045201b0fe9b", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981e61f18677be0814e1e5201a6b8c73ae", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dea7f1898afb339bc4fd34cb3719e9fb", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980d3edb6d5b181372d26f00519a1d9fbd", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984bd10b64b5245904fa6b01f740a38383", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ab054c686b6427bb6216b99fee55fe4a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989cffa10094abb4f4ec47eedba9bb0ae5", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987a97f49f9812898d0b26eed295d9bbef", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9870cc133c6a1f1383626f1b32b5b5bbf4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98426f209501b39185c6fe7985cbe61a5d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98168ff98b4a78e972a2017725ff141a0c", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_timezone-3.0.1/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98cd1c47b9fc85aca65d593cf6148a7afe", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_timezone-3.0.1/ios/flutter_timezone.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98f14d222209af5863976ff0208fecc1f6", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_timezone-3.0.1/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f2548309661e2e450a3cbc8c7f52eab5", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e987ebf166e4eece98924f8377d945ed14f", "path": "flutter_timezone.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b0b82a851a57f01e7b53f7ee545e22dd", "path": "flutter_timezone-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98f3072b7a302f3b78844af6d148ec776d", "path": "flutter_timezone-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988c1e0a1fa083263728034797d7f34bbc", "path": "flutter_timezone-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9893cf71b5980205c70bd6fd930a9756e3", "path": "flutter_timezone-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981263734361cc2e016a7ba7dd8ab7c097", "path": "flutter_timezone.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98aeeecef4004e0e70f1fd7d88d909b24d", "path": "flutter_timezone.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980c6aad6071b68467446d7ad453df351c", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_timezone", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988bf86dcef3ac88a2a21eca1b786459ec", "name": "flutter_timezone", "path": "../.symlinks/plugins/flutter_timezone/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989f712c4d2e996f79987a079a7693ce9f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/AudioPlayer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982a6ef9a2846c09e80289747794ea908b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/AudioPlayer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9804f9ace45079a4facb6056b6f688a010", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/AudioSource.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984a56a270c56db6952dd41e5bc4bdcc74", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/AudioSource.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dae6798eb789c8cfb92b77debb01c45a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/BetterEventChannel.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e63e93b637bb10368d0625520637181d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/BetterEventChannel.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983fcbe82fd0a121563668121ba845d76f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/ClippingAudioSource.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98db1502a37f7639ecaf04822de68b3f67", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/ClippingAudioSource.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98419cb89a0ec223a61c0430733c6c17f4", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/ConcatenatingAudioSource.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9858da65142518584f0b45f5e07a183308", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/ConcatenatingAudioSource.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a493045968c800b79507b482dc6d7a53", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/IndexedAudioSource.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f41528419a3fd4a626ca33f3628abbb9", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/IndexedAudioSource.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bb40792f741eee43ee6f7d0a5a0b751d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/IndexedPlayerItem.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9870e0bae0ade2a7611e5406c9e02e8885", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/IndexedPlayerItem.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985cc01b0283a3a7551c68d96a283f5862", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/JustAudioPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983c4f2a486f8956215f62186c8a9f2329", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/JustAudioPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981dba5177fa583ad1361ed8f7a0e5e778", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/LoadControl.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b72838c449dda971a35121d41fc43578", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/LoadControl.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b34ce867e20c3293540546a816882b7e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/LoopingAudioSource.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98140e93e2d519c856570277af5e2950da", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/LoopingAudioSource.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ce1c4c7e33adc61aa4b4fed199bcacb3", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/UriAudioSource.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98061a7dc53fcc489c73df2ed6edce49ca", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/UriAudioSource.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983c5dbd28d70f779af6aa06f8f86cb289", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e3a0032fe102c9d33947c079518b0750", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d0d8062089a9e8c70989db2e370aa840", "name": "just_audio", "path": "just_audio", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986c284b0dd9fc28f2ae8441b42a28abdf", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98127e280a8a46486b374604dd8e850fba", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988cee4aaeff8a2d562ed9a165c693dd62", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c984af0c96d116be0f79d566d483f18a", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d2609275f976618c208b5fcf58e2ffe9", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a3f73a709f27a56f12056bfcc9938683", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fde702e6d6cbd8bf83afe4d76af61c56", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9808ffdacf0e8837eedd8281b4ee4e1268", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cd0f73e4bb7eb5ceeaa4017d82b974dd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ac688cbe62ac0eecb130271c90b8d3b3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f156587380dd594ca832da9ba4cf8169", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989eda8657d48159bdd9c08fe31406f3a5", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9837cf60b874ed0261ea7f3df515bf9075", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/just_audio.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9848d5046c443dd2c1b316a433c6a899a2", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980b5bd01b608e3f4f5347407909575b6c", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9806a1de76d1a7f707c4e63e63051ac7a1", "path": "just_audio.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98031dc0202a90a477b61034bd7faf42a9", "path": "just_audio-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9841d5d6e53cee71f9f808e9c7bf4368fb", "path": "just_audio-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98226a26070895a1c5848d045793b89521", "path": "just_audio-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d4958659658d9883e3044739d783290e", "path": "just_audio-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e987010b81292a2d50d3fb8d0efd422df47", "path": "just_audio.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98060f2b4cbfcbd4975fd07e90f17277e5", "path": "just_audio.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d73ce8689a41cf2d9a8a08fd3d60f2ea", "name": "Support Files", "path": "../../../../Pods/Target Support Files/just_audio", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989a5b1dc6bad230f1a04d5724bd4fca2e", "name": "just_audio", "path": "../.symlinks/plugins/just_audio/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985f5a851d6b3a017290d950f79846f3df", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/package_info_plus-8.1.1/ios/package_info_plus/Sources/package_info_plus/FPPPackageInfoPlusPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98b7502cdb2f5cae8f8fbe6225b944183f", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/package_info_plus-8.1.1/ios/package_info_plus/Sources/package_info_plus/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98300f8d4cf1e3ba9e035583ebd34f9824", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/package_info_plus-8.1.1/ios/package_info_plus/Sources/package_info_plus/include/package_info_plus/FPPPackageInfoPlusPlugin.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fda083f5088ac3145fa15bbdafb2bba0", "name": "package_info_plus", "path": "package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988ea39795f025c881cb947148f079deb0", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f54dc5f44b1bfdffbc3913d7a0e77fd5", "name": "package_info_plus", "path": "package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a56b76cfccc634a05b37a367f3b9b266", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aa2b103d143afe33bdfeef3819b9a052", "name": "package_info_plus", "path": "package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dcf1980ec5d778b8e58d1443f801d522", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a4f8a1c1fbcc7ea8187c4b75922e7231", "name": "package_info_plus", "path": "package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9811a0fee2aef8b8d8981dd93b0b35dbab", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982cec49a212c663978412740e0eda70f6", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980c5b1bfc75d65772bda38c2d989d5003", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98214fedc2405d9ff1a35d5bc7d922dabb", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9833afa634298afd2629329c69bee670b7", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9811952aca9d3d18c33d25069da760862c", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986548cb261b4e7d0d52ee56fbd586c7ed", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cbe1ab497d46f5f887316ab9edb2376b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cae3aeb04c71f8f782ece45e871f09e0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f6c4c14abd8c00e36ae14ce8fdb19d04", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982d5641b55cf4bca94eefb67db611bf8f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984cc3b7a2f634d00df950c795981e4698", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cdfd025e97d8c9b561c1c8c5fe232ac6", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982fff24b73875efbda89e73d5954bf47c", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/package_info_plus-8.1.1/ios/package_info_plus/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e984676ec21f55e07b40aaf7176d7da6645", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/package_info_plus-8.1.1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98299957ee97235e403ed6021b8961c33f", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/package_info_plus-8.1.1/ios/package_info_plus.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b76028f187d6cfae033873c3032a38a4", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e988187ac7884aa68e43e00b78e0f667861", "path": "package_info_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9849e1fa48fe4eaa1f0655dc415ff84cd2", "path": "package_info_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98d78476725b031c3f16649528fcb2dd77", "path": "package_info_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9822180fe43ffccf03c95ec8776ecfd73d", "path": "package_info_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987b855a718bf468cb6a97f65638008b7a", "path": "package_info_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98984bdfbbe21e67ef634094259093fe2c", "path": "package_info_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a85f6f9f54b3c5b75d9d4a01a570340e", "path": "package_info_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e987312a65786db86bd2c3472a5f867b3de", "path": "ResourceBundle-package_info_plus_privacy-package_info_plus-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981ee21458e3328f2b31d609b5d7059660", "name": "Support Files", "path": "../../../../Pods/Target Support Files/package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985e7127202447d660b9905155d69948a3", "name": "package_info_plus", "path": "../.symlinks/plugins/package_info_plus/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98346cc0ab2f1f4bb61824a8c45f95d682", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0/darwin/path_provider_foundation/Sources/path_provider_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989730aa29719ad12f72470f1c21262264", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d7a9d403b50b0941366b7048f33d7a5d", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982374db2ef1e9f6487807808ee5d50d43", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9837fb27c202cd3a6a823c93623a63a004", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98edfdce9e5becae8a37a2388b06a63724", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b6f8444470da5406a6288c847b88ccce", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98be4d3c177e62669420e877bf906c0313", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9816ec34b6c88d84b153ac5b638f7abe9f", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c7018335f9dae8bac3e2192ac4ddca27", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982ddc3a61ffee676e7182644e0cfe3b00", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e93e59e4d3d251ce1f60ccdd95e7fdb6", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981695ae4da223f2986c44fd89212b6e15", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98903bc743e18dea1cb690bd098f6f7fcf", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e854bcf777fc4c003b4a3cdb63fa0dd3", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0/darwin/path_provider_foundation/Sources/path_provider_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982cafe3718943464a59dda46940c5cfde", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0/darwin/path_provider_foundation/Sources/path_provider_foundation/PathProviderPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98be520d317ff239c91093dcb33c915b66", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986ed8810ecc073f776c1c7ce5f630ab5e", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c057da992f4fa66b8ece65db833108bd", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98af124d185fdfd78dd2e7064811c6ce69", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9833bd628a79f943acdf5b90f689cd8317", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98654d48b6978dd5ccaff64b14a03ee0ea", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98076dae7120d3c33e5091097504afc58c", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ef8bd2035c03109c2b1e4c0b344c04d1", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9891cd1928bbae617b059365c402b035f6", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984b5d9f50efffff2e6c3c4449110baccf", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a44b7be2ba8567960714a302dec20cc7", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ffbdfb518fb0df72cbc99574b24c4614", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98668f552816032c5e5b39e1a329b6e35c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983cffaa32eaf6a15f2afb4cb9a2829860", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c314c9431b32bfbb1665d5f4b4a3017b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98af5072913f9cdaf06714c6f867f00ba4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983843d2cdc878feb515a899818c79fb65", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e0e6ac0b07caf7d24a916e4efb0160da", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981d06366274086793dc3efb3f41888e23", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0/darwin/path_provider_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9893300cf2bd36e5efed793dc79ffb84bd", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98c8f78c8b369036139993c61f567a9ed0", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0/darwin/path_provider_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982f45c3ddeeaa43d9ce6fadd8687c2dc6", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98163907ca4783566f4663c20af0e85e3a", "path": "path_provider_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983567f1370727c5c6a8aaf7065071b753", "path": "path_provider_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e986b630a588bb297ee270430f34a91bbcb", "path": "path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e0601979fc6d8cdfe2cd9ae39b721dec", "path": "path_provider_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a9387adcbdd263acc68f2e759222e477", "path": "path_provider_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9832bf74b3878465ed43bb930ef5ab5820", "path": "path_provider_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9855537a6b8b6346ad637bf0974f21d67e", "path": "path_provider_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9809d5dfb52c0b36fccc8cad17c7f46807", "path": "ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9859b00cdcc108f582f5d410a71d5d6ed0", "name": "Support Files", "path": "../../../../Pods/Target Support Files/path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e415afc4c862eb93c5d658f242455dfd", "name": "path_provider_foundation", "path": "../.symlinks/plugins/path_provider_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987a7eb3eb130e4b9a3eec632dbf6119d4", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/rate_my_app-2.2.0/darwin/Classes/SwiftRateMyAppPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984b78c887a721080aa419b934615a996f", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ca5931f1f4f93857fa19efd9a075dc47", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f4629bf3eb13dee43511f4deac5b128c", "name": "rate_my_app", "path": "rate_my_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985e23e75cbe26536043caa61dab9431d6", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981f67a17efa211e805c5a602f1f528f79", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985fbd6bd85e5881f49d378be91429176c", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9848f8cd987d91f3c3e047c5827cb9ac5e", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98174b4355abe4a51b7cee2ae4a7f30f3d", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98876c5f23d80e4351503d3fbc7f1f72d3", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986d5e6e6865393f669aa813166c89ba6e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988c140a95fcf6cc41ae513cc4ea9bcfb9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f318f71b6595c6510838a485b02212f4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985e713117f69488f0e3b759e4149d8ce8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c21a9385a636c0a3b29868dcf8fae6df", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98300a4d9248d49679e8ad00027f5faaae", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/rate_my_app-2.2.0/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98d76362061ee304cfa1746c45f2eeee91", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/rate_my_app-2.2.0/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e982f1b6f3446539c097b0bd631d1efe9a5", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/rate_my_app-2.2.0/darwin/rate_my_app.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9806556447ba1349083fa4e1a31e72a6e3", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98ce70e074058777e272ea6b33577bc0d8", "path": "rate_my_app.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9884a895ce665c7a101927957fa47625ab", "path": "rate_my_app-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9889b5b8285ca5e82574f44596603fd56d", "path": "rate_my_app-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987600c470b76a9e741b2136611ed11554", "path": "rate_my_app-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9832c5f103a34a741b5a80f03e6faafd5f", "path": "rate_my_app-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9859c8942cd2d5b4acfe61ff05cd70a029", "path": "rate_my_app.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c245315b665056ea9f3e2de4676338ff", "path": "rate_my_app.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9898dd9578eddf76eaae572b347b24b3a5", "name": "Support Files", "path": "../../../../Pods/Target Support Files/rate_my_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9837ddfd584cba4eaa98e312db32a2c78e", "name": "rate_my_app", "path": "../.symlinks/plugins/rate_my_app/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f337afa6be8e3220f6b495905566c39c", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/share_plus-10.1.2/ios/share_plus/Sources/share_plus/FPPSharePlusPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98acbbd848bdedbe8cc5e6911fac0e417a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/share_plus-10.1.2/ios/share_plus/Sources/share_plus/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98735c1b43bc4ad8cec1202f88a9ddc4be", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/share_plus-10.1.2/ios/share_plus/Sources/share_plus/include/share_plus/FPPSharePlusPlugin.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98234ddb8c592eea099354bde14272a3a8", "name": "share_plus", "path": "share_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ea342940e9009073872ffd1ea9f3a5dd", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98de3b14a1cee90e7bf174e14ec54431c3", "name": "share_plus", "path": "share_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989e4670a69e80a28af1ac29362fbebfbe", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98367bc007b083c786b2810acbb16afcdd", "name": "share_plus", "path": "share_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9880c90b0997dbf107ef8bbe3afad3482d", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e982011b264c895398a1042d5392ae70", "name": "share_plus", "path": "share_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98caac47503ddf0308d3687ce1653784b8", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98690081dbe2dcffbda979c98dd415dc9f", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e77fe683eab4630b042ba2df22bb5ea4", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e166d297c5e98761db882aaa84bc6f7f", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ec28e692cb7da5d703a0314ea41a85cf", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984f041436d6245ecf91c80776e9a910ea", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981bf9ace87972660a0cc8c8125bbe1140", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98231ad27366f3460eea417ec534cd4e77", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e5f2fe883d1bf322ef6eef948054fe6c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9821bc61da9773fd383f1fcda2c412da7c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98303377e4abb99b49b729189f6efa6b70", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98081de2bfae011a72eae310001f9da113", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e9a653332a66aed665fbfc958adbf543", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b4ebafcc453017b54ef68f568697c690", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/share_plus-10.1.2/ios/share_plus/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e988f90373383e1d6dbf6abfc1f389fcd46", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/share_plus-10.1.2/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98596b2f8e10096ea1456ec4e8ea3d5111", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/share_plus-10.1.2/ios/share_plus.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e33b9b6d4c0046cbf5587adbb64d976b", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98839796be73061c7dd9900fbe3b6d1b4b", "path": "ResourceBundle-share_plus_privacy-share_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e980f1c42cad7e228d7f7a0ef9cce061c2c", "path": "share_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ca6bf6574a1a0322446eb7bb13322d75", "path": "share_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98f0d8ca977b09a6d98506265eda8de729", "path": "share_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9833cf03ab55fe5df7e478e1f47f03f08e", "path": "share_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bd216332e829830b54f46543c005fbd3", "path": "share_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f32a175c3098a43f300ab83bce178f55", "path": "share_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9892bf2b08c059868deba41e9f4131938b", "path": "share_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98bc634f942ca199eb284508df183675ed", "name": "Support Files", "path": "../../../../Pods/Target Support Files/share_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a4e70e69a5731245ba967270c9ca7c63", "name": "share_plus", "path": "../.symlinks/plugins/share_plus/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9833f8c6fd58e30e1a4ea1b0361ae68c6e", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.3/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980b731a0af2f35d576f3b983eeaa5074f", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ca5b98d97ad3fd2e1cf903b68ab5f8b4", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9843f98672975b05cf80ab3bc63cba473a", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c8e009f5a52e14f3c07ff67ff5e10372", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9823892f0f29b644d81b37361ba81eb7c3", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988cb73a5201f9f8277e22c71289a1c041", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987a735f78004b87fa2275389ca4786b69", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9868ea882c4a60573b4bd3e488b10482cc", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986b3a7a8de6eb4774b3c8935b4250fbb6", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e8948e4306cab101992879af7a7640f2", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98af9e2d8ed5f41b1b22c53c2543a6d0d8", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fd8a2002cc67afb02b41c11db087b6fd", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d82703e0284181c99ba7ce7c170beb0a", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a0e56b99c82d92065e1964dbc838b171", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.3/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b71a18d18448c152b2ff590210aafe8d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.3/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/SharedPreferencesPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ba8f40cc17ac9908b718c5df98236deb", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cf14364dff0a5de350136054591997b8", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98adbaa87909b6870634582fe374cdb7fa", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98576e3f7a2c29d11047486b312ed8e747", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981585525725758ba977fe6cb156438a73", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e05044c2c528caf90bd691f86a6bde37", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98589482554bd02d3df3641316dc04277b", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9820f0d63adabbda6352346551eb0cd9c2", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e07810d1cb66293c6e7a33253b3d06ea", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9814fa419c8c3b6d81d76b6e5dc341f5d4", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9897b805cf0e4fc09c274e6620dc461c67", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983630f73b30a888da5de77bc7fd452a60", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98baee001c5769ddf7e71a1679ca6783a7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a45e4224b4ba789419c871e36ac2c9e2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f698940e5ee9f13bb5d7a106792e8c77", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981cf31512a1a7042a7c60a94419014a80", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9826652f465bef29268fa6efbbf053c4b2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c84113eafd59c91ebbf6cd06a5fdcb4e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9853d997d6e61c38d62891b72bef7c4955", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.3/darwin/shared_preferences_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98363835b2a0c6df1d0a3bbef3c4c203fa", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.3/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e983adb31a45db6ab69b80119fe53247a30", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.3/darwin/shared_preferences_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984d3a0aa8932000ee07f4266ba740f819", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9803724501ed150b7f5068f4b5ef1f6072", "path": "ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e986ab4e0644bc5a53207a242aa166607fd", "path": "shared_preferences_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98143bcfb96aab361c10c8ec76511ad431", "path": "shared_preferences_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98c9b6205d11b052dd720be69a8542fa77", "path": "shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c6848a567b3ea315376473ca9aa74690", "path": "shared_preferences_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9871566299f0787230d06fce1cd38156cb", "path": "shared_preferences_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980259b68fa33199cac67d8a6ca9c446f6", "path": "shared_preferences_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9891c56158a0a6dab4e90355ad1f329cd0", "path": "shared_preferences_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988355a3ec9dd20e0a576589151c5105b6", "name": "Support Files", "path": "../../../../Pods/Target Support Files/shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ef243a6cf9ba05323998725bc5208ea1", "name": "shared_preferences_foundation", "path": "../.symlinks/plugins/shared_preferences_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982bbfd23ec683de0700bf3ef81043e0f7", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/simple_animation_progress_bar-1.8.2/ios/Classes/SimpleAnimationProgressBarPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9895ec758c5443d2893d193730e61b316c", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98780cc3d92646361cb0977e93d7059b8f", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98daecc057099b991f5f6fd447383de5d0", "name": "simple_animation_progress_bar", "path": "simple_animation_progress_bar", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987237afbae384a618d2456f8843ceb4e1", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9861bdbee7df76fa33a2611d3229f8b91b", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fe4e90785042ba29195280e7631daf2e", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98749b189a974a2cdd3eed046ad0343ae6", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9865ead303de79e8447ff105097745125c", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fe4f0b2f3d37bfd8b710aa522b41ec78", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98db2f3ce35d0912079fa4982ef5ebc3ad", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b1760e5677454b91853d5b01c02efc45", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e6bd874fe8df5ecc2acc3897cb6bf351", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d6d71f8ea29ab4f4067960d5077f0bc6", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986174df08c152d73afed76c07bfda4210", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9850cc1cef808c29299f7a9a447950d64e", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/simple_animation_progress_bar-1.8.2/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e985f44a5e0b061833940d0c50899a48027", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/simple_animation_progress_bar-1.8.2/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98b85d4d1405a831d9eda6ef44c466b278", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/simple_animation_progress_bar-1.8.2/ios/simple_animation_progress_bar.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981e25699352c978d6923ded302b853161", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e986719b3ba5211dc5bdfce1f5c3ad2ea0a", "path": "simple_animation_progress_bar.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c8d7de3b31f1563aa583c8b0aa935bb4", "path": "simple_animation_progress_bar-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98fad93648d3aa3a44c31a8e4f0b8d5f8c", "path": "simple_animation_progress_bar-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fe9f6d3d507da7b25bbf82edf0198225", "path": "simple_animation_progress_bar-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9829ce895e1e5a8931f950bd632f4cb25d", "path": "simple_animation_progress_bar-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9847124287c260e478809d5312b7f936a7", "path": "simple_animation_progress_bar.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e985531add0fd1e6122ac74ee004839979c", "path": "simple_animation_progress_bar.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987f236963d1320be37c370de2a50da0fd", "name": "Support Files", "path": "../../../../Pods/Target Support Files/simple_animation_progress_bar", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983c257466897224392dcd660789e90d2e", "name": "simple_animation_progress_bar", "path": "../.symlinks/plugins/simple_animation_progress_bar/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9899d31ebee19d05d1787a41f9700d7625", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98384c36e3bd45e9760e1a97c5fbc45db8", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9869ffe12d77448097f5203cccaec70496", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983b9adbebdde14313c44153611dc1dfec", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983f384a094399f0be331e88c54d75400e", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9895fe1501e73419c558c32bf7dbb14850", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d39363768c3b90cba6a88ba4c1050539", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984a18305cd08d2ac57dbb3d1cff91d336", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986650a3e831a64647330acd3a9f93c56c", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988f93deb622b8bcd5927d41ffd73f7d8f", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d32b3a4091938966341e6988872f33c3", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988ba488b5b79211aada9ec27b006a3164", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ecd00c8ed758edca035b7a58b421d90e", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c512c736bc5440265353d0f55b623293", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a0ffa2767fed7ba12d16b52d5e1f325a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteCursor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cc2edf8e8571ca7f2c76a9cc4437a8ae", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteCursor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f232b9803cd234c06ca00abf14d05296", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ed070f353d5bd986d6d813613140385f", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c625aa2a75b87a8b8338ed64667be48d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9891dedce9c2b51dcb60b26f5101456797", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseAdditions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989e1e052c29a9c890852c719788e92470", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseQueue.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9872c89ab67af336f43144ac4c93e19907", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseQueue.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98671fb7cf78799813016e5bb1d71f673a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDB.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bc6408377a64c01555accb20e773be13", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinImport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e7049a5cd4d9e5d4993372ec2843d690", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinResultSet.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9875cff21b1c015e59362b6eb24d0548e5", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinResultSet.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981c9229a3c6142785af4a28d23e6038e4", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDatabase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982ea8e666f6ab8168f093fb2be884af4d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDatabase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989b7bf1662a1c0be198d3f6c0cd8a8b5a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteImport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ef99bec315a2558ade73724226a4e606", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b8042cc043c2d9257fe7833585507a2a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f7ed715a60afd8da824890ce7a6eb416", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqflitePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fae46e23eac5b9bfb6ad2b8fe9ea84eb", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqflitePlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985ca8ada56c95980c1cb5b1ba249640b7", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/include/sqflite_darwin/SqfliteImportPublic.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988ddb88e0686d49490239a60364086c2d", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/include/sqflite_darwin/SqflitePluginPublic.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989df2699139651d63a8711a220df3b044", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9860c5f5f8778430b3b0017900df7dfb42", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9819194a0842ed1651a2120a6411d095a0", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9854512cd24f3b91d56f879676792067ee", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986c3e8b775ed2a1fba87b04695131539b", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ce1973d3f16808d2ad439792f14d3583", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985fd5f81dbfac6f7dbdef215c090b164b", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a9db910024dea9ca0acf229de9027108", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984230c36b28d44a2b31a5d2f5e56728b7", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984e4f9e0f637afc1936c16d7215f3dfcf", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9880a1b034b925c12016f4d8d9616d4497", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984fa745b54a1b9bb5442c836b42ef4542", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e4845f99c792fa875ff2a713ce64e195", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981e5af6b59b2597a7ebeeeb7f0fd79e2d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9880469eb9e58b2bb93aceac01035eab7d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c2262615e4f8d8cb50284abf9fb7de30", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a6270058a489c48cddb948bbb54372fd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ed1640385370646b815c21d277263ef9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9811283504d3a749833bd6ab1233cb61bf", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e1c1f0d557b6af96318930c71b85e94d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bca2087d3f47d0e2fb35074f1615cad1", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98006dbb17506068c054be06afc949be7a", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "bfdfe7dc352907fc980b868725387e9881d98ae58a6d0512cb78fface4f639b9", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/README.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98fff4ab389e03224e40b459dab1d27552", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ce1c215b8976e4f84395525b9d85cbee", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98f2dc80dc4dcc60141cb10f09a403e29c", "path": "ResourceBundle-sqflite_darwin_privacy-sqflite_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98a82b28b0a12d6421995cc5f5d6cab3ce", "path": "sqflite_darwin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984c759f8198f6b6083932e2dbd7db86ab", "path": "sqflite_darwin-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98e640a1d189aeb67dfb09abcf4281e889", "path": "sqflite_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ed1d50dfe4db5e379d265113dc965459", "path": "sqflite_darwin-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98755b319d7fb2a60600409a06b4c3795b", "path": "sqflite_darwin-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98207d0250060e83408c6567d99d1151fc", "path": "sqflite_darwin.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98df4ea762b380bfae5609131771955c6c", "path": "sqflite_darwin.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983f00f61ae821c4e985ef2ea4d2145bc3", "name": "Support Files", "path": "../../../../Pods/Target Support Files/sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9873eb154e2b1d22a290bd6593d751b912", "name": "sqflite_darwin", "path": "../.symlinks/plugins/sqflite_darwin/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e980153929502dfd539f0d168d2cf483847", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.1/ios/url_launcher_ios/Sources/url_launcher_ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c3a1c0fcb427858e805d618f303af767", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d45e57eb650a818ef9c056bce030d0b4", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f1cfbee0e10450453cfaf2f284a03578", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98956378ee14833fc76c0348b2121e8c85", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98402b0476a4256af41e9eeb4c74742dfd", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d45ffe6a68a7cfb18a6e3cf84e2911d3", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989dc944e3964259d3fa92c84c7d87ff95", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982ebc2661bf24712e20e1c5111eb4545d", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f12d4bb9843725e9867ef65562ba494d", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98086f6d8ae9e423b5262aa59dfdd809ac", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a2113041e72b42f8492f289a8b716889", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980733cd802a1168e29169f5a973f942b4", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a8dc6f023453f5d6947e1321c50e5215", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b5839d06523e8aa697c33daee041343a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.1/ios/url_launcher_ios/Sources/url_launcher_ios/Launcher.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98974ff5f5906c078d44fc65ca0df3af1c", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.1/ios/url_launcher_ios/Sources/url_launcher_ios/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98904d199fe22db396067e2f6d1cbb8ce6", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.1/ios/url_launcher_ios/Sources/url_launcher_ios/URLLauncherPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c03439fd9aae0808d0e939945b0f8626", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.1/ios/url_launcher_ios/Sources/url_launcher_ios/URLLaunchSession.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983ecd58372d41c47d6b391a6d0b653d90", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989ceaf11a7343e40ec05fdb5da57f627b", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ceb442abfc7fffe111b59a094d284a25", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98480727934ba7e2965b0597fa2c1e66bc", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98902d8599345985dc71e70aa006a41590", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d3c2c10b3f634c04b09de18d9ca0abd1", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f8f69065832bf33ac475931e7168dcb7", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fef03ede98d14ad694043a82ad175563", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9808a2fa6174933b308d0dfbadd28c69f3", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980ba1909aceedb501244d24b07690d789", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9881599379021cfd76a87192a8b10f3c35", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9884d1c381c2cdb4185447522f75fa3e3d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98814f72ff1f938d4975f82b8187eadba1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f3d09f17d66ce4b3067e7e2df6af2d92", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981fe705d0a6235a6fb9121ddc77181345", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982495af119dc3ba147ef01f7da2f7f48c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989175eb9319261fc99231625d82eb6b14", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b0220cf140933dea307ac75a05b34446", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988bb000ded7c09fd5e3a96fd0d0d22e58", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.1/ios/url_launcher_ios/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98c6a0f95671793ca792af230e80c77fef", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98e7e3be8b345e4b3b8cbbe86d0a2fd572", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.1/ios/url_launcher_ios.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984dfa37bf7308195a9203c7189cb15f07", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98970ce319bcc96b574dc1fc6d5824629a", "path": "ResourceBundle-url_launcher_ios_privacy-url_launcher_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9868d15657aad05600ef866831374fb9e2", "path": "url_launcher_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983523adc0f0f39532f5dd219df1de6e32", "path": "url_launcher_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98115a0dbb5f1a86d56e6d02f496bdd0c1", "path": "url_launcher_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988e6518b19d0b46df57962a83f8709c38", "path": "url_launcher_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98daacc4b3aab98710299e3cd16242bc6f", "path": "url_launcher_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e987319b44892bd9e845957c7a6607f534e", "path": "url_launcher_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f88625c3fb3cb7e172df0f113eb1f377", "path": "url_launcher_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c4b52d6c7168e64ce0e7c1022b9558c4", "name": "Support Files", "path": "../../../../Pods/Target Support Files/url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9864d1520eef4d8fd42ce44ed6fe20be21", "name": "url_launcher_ios", "path": "../.symlinks/plugins/url_launcher_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985d7a346d68618b48a2d2e7f9c13af90a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.8/ios/Classes/messages.g.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98652d63713cf934d885f2c50ce5efa81b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.8/ios/Classes/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9852a6b378a2430c635d00ee766bd1c2ce", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.8/ios/Classes/UIApplication+idleTimerLock.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984fd045dccc3a616cb149e88fc1728fc2", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.8/ios/Classes/UIApplication+idleTimerLock.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985e95ca04d1d87336afca2ae25567d482", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.8/ios/Classes/WakelockPlusPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985dd4096b19e59a43ce4e9638ed939da8", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.8/ios/Classes/WakelockPlusPlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98007dbebe25b01bd811b2ab09fe31413e", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98669118f03e7f033be7abc75c8ed4e8d8", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.8/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981d438d961a1fcaf8a74289b59e5a57a5", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f28525d09bf68e84ab4b277602042093", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c2c5fd312a0bd62973574cfa26194d2a", "name": "wakelock_plus", "path": "wakelock_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a36de62e9df177ddc8e81df6a458a245", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c00ed4d52bea9d9d978551c1168c0191", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9834748c39c7e7e52a1e0407c88e9d6643", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9847c6e23dac957f11265ba2e79fba64c9", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986bd80bc3353b74e62c960e9d6503dff0", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e3bfd2c783cce50db758afcef9859370", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b620c64b6cdee746e1161414a43174b5", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b9e08e6d9f0b86065f0171377a481d16", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b5e73ae4a3816307eaab73a273d42190", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98998b2f1dc57f753637c625cf806b7e33", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d0e8e12d3f49e7b73f5a7412f491f8d7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985d4670ebe3835512cb2b833010094dda", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.8/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98646a493bcd8b0b3572baf8491efda6d6", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.8/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d0baabea83ce32adc147fe62ebbb87e0", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.8/ios/wakelock_plus.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984ae551456688f3476d37d21d52aa35a5", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98fc97fcca28814f8e5c9ab7b0326b951a", "path": "ResourceBundle-thermal-wakelock_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98144c90cecd194b250e975623ee38cde2", "path": "wakelock_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9812a7794bf794d6f165366541af852ca4", "path": "wakelock_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b1c2962e4e5197df514d13c76518f973", "path": "wakelock_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986749f156e5ab922009e85d8104efc70d", "path": "wakelock_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986d963570c349b234dfa65da65f8275e3", "path": "wakelock_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986dc8038348ad7f6d565a70ee0705b674", "path": "wakelock_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986449105b5646a530dd9c1a3710a97366", "path": "wakelock_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987fa8344f0c58bdacf7533784d46ec9f2", "name": "Support Files", "path": "../../../../Pods/Target Support Files/wakelock_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988cb6011d30a3a63bd2629ebaee24abcc", "name": "wakelock_plus", "path": "../.symlinks/plugins/wakelock_plus/ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98efba850693023493c17872a12de72e65", "name": "Development Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e9867aa7825f197b5c58a57320b4ccdc64e", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98942867ef9a9b82a20a4a7fc87d922201", "name": "iOS", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9862d9c32904d63fdaba0f2aec66ff1562", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a126abdf6b3569d865a3cd90f7fc062c", "path": "IosAwnCore/Classes/models/AbstractModel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989b7decd87d1efb353fc4e0dbbf7b6469", "path": "IosAwnCore/Classes/managers/ActionManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bc5d0eb33cdc6773d458a59e0e3e63f1", "path": "IosAwnCore/Classes/models/returnedData/ActionReceived.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983d94b1b7adaad07571b07d073285be1f", "path": "IosAwnCore/Classes/enumerators/ActionType.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98526243ac14affdd5fe448137c7667704", "path": "IosAwnCore/Classes/utils/AudioUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984a7537b2f59e40b07e10dacf662dc93d", "path": "IosAwnCore/Classes/listeners/AwesomeActionEventListener.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983635b24bf849e0dccf1255ad48f83c9c", "path": "IosAwnCore/Classes/extensions/AwesomeContentExtension.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980a95747614e792e357ee985f4c7a059c", "path": "IosAwnCore/Classes/listeners/AwesomeEventListener.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9851f5bd10f5458942797da6e622409be4", "path": "IosAwnCore/Classes/broadcasters/receivers/AwesomeEventsReceiver.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d61e5a6c74daad9a36ac60ae89fc275e", "path": "IosAwnCore/Classes/listeners/AwesomeExceptionListener.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ee27e6b14b11c2dc93af63e6f9689573", "path": "IosAwnCore/Classes/broadcasters/receivers/AwesomeExceptionReceiver.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a72b6ece86ab4c28ef6232d731d525ed", "path": "IosAwnCore/Classes/listeners/AwesomeLifeCycleEventListener.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a9acce869792124c53a022b0a104e775", "path": "IosAwnCore/Classes/listeners/AwesomeNotificationEventListener.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c070c01bd2991598d65a80a62c41eb5c", "path": "IosAwnCore/Classes/AwesomeNotifications.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9881aa40cc3b9d2115a507bd8825648191", "path": "IosAwnCore/Classes/exceptions/AwesomeNotificationsException.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9883cc9735a7349857fe7178ddc6511b54", "path": "IosAwnCore/Classes/AwesomeNotificationsExtension.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98496ca2e59726f58d8f18f34ba612d7a8", "path": "IosAwnCore/Classes/extensions/AwesomeServiceExtension.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9821c32645dd371cee43c330f736a195f2", "path": "IosAwnCore/Classes/background/BackgroundExecutor.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9897c2fc5ce426e77cb46af84cd3b5e914", "path": "IosAwnCore/Classes/services/BackgroundService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c1229aa8a01ea73e3dfc48b80f3629ce", "path": "IosAwnCore/Classes/managers/BadgeManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e1211da37de43def32f68f49072dec3f", "path": "IosAwnCore/Classes/utils/BitmapUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98126aa1243265879b9688ac1421b5c5a6", "path": "IosAwnCore/Classes/utils/BooleanUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fb0563249697034bf5464ea9ef638912", "path": "IosAwnCore/Classes/broadcasters/senders/BroadcastSender.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9832c2f23424d2912e4273aedfbb02b2fb", "path": "IosAwnCore/Classes/extensions/BundleExtension.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d64025397049b20ec0d5efae62b39af1", "path": "IosAwnCore/Classes/managers/CancellationManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d81fdc8a42ddee4b9e22e9ee36d0ac6b", "path": "IosAwnCore/Classes/managers/ChannelManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980e8f8968b713e7cbd2a706316bd7cd51", "path": "IosAwnCore/Classes/managers/CreatedManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9858f1aac34273bee44bb32e1e96e09508", "path": "IosAwnCore/Classes/externalLibs/CronExpression.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bdaf57029c5f47a0ff9311e5c3bc25c1", "path": "IosAwnCore/Classes/utils/CronUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98eaea53e9606f8323ebede85e6b866535", "path": "IosAwnCore/Classes/extensions/DateExtension.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985d5e808fff10d31a1b68d8e7c507e2a2", "path": "IosAwnCore/Classes/utils/DateUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9863899677a838e6061e17f62d4c44bf49", "path": "IosAwnCore/Classes/enumerators/DefaultRingtoneType.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fc5f6e6a0e4d942e8fd7d17da1a9e805", "path": "IosAwnCore/Classes/managers/DefaultsManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986021d4c31c4c59f89ee38fc7fe2dacf4", "path": "IosAwnCore/Classes/Definitions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98953f6f4e32c4cc73bf17d59da28f1a4b", "path": "IosAwnCore/Classes/managers/DismissedManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986bd65fffd792c32435a7d5af67dd8db2", "path": "IosAwnCore/Classes/broadcasters/receivers/DismissedNotificationReceiver.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b80153769011f31288d94da657c3baf4", "path": "IosAwnCore/Classes/managers/DisplayedManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ba44be124691e0af55dbdae0004a17e4", "path": "IosAwnCore/Classes/utils/DoubleUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ff0b102fa239a538b9a8c59cf471b6c3", "path": "IosAwnCore/Classes/utils/EnumUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981c6c3c3707fed8f122b616b5c7e84260", "path": "IosAwnCore/Classes/managers/EventManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9893ed2457d8698322ce26a54f35eb1a0a", "path": "IosAwnCore/Classes/exceptions/ExceptionCode.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c8e6514373a838e2401611fbc8aa0aeb", "path": "IosAwnCore/Classes/exceptions/ExceptionFactory.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983c626e2643054acb571d9ae79d348647", "path": "IosAwnCore/Classes/utils/FloatUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980be318acbd8199c6bb4c351c1e55a97a", "path": "IosAwnCore/Classes/extensions/FormatterExtension.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981e651c2e3999ed88a841a9ec1739e087", "path": "IosAwnCore/Classes/enumerators/GroupAlertBehaviour.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98af7bf9b98f13b6b48b4390bd1e2c013d", "path": "IosAwnCore/Classes/enumerators/GroupSort.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98601bc7fa1c1922ea0ba58d4d25d8c207", "path": "IosAwnCore/Classes/utils/IntUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983513c0ac19b4708cc09c6e5a580dc9ca", "path": "IosAwnCore/Classes/utils/JsonUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d9d3f61dc9c1cab59521e36b571ac9a1", "path": "IosAwnCore/Classes/managers/LifeCycleManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98064362b70c676a49b5dc94ac5cc0cc67", "path": "IosAwnCore/Classes/utils/ListUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9890c0863ab97eeab9ad675db4425c79ca", "path": "IosAwnCore/Classes/managers/LocalizationManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c95be738dcf4687572111541a3c07a02", "path": "IosAwnCore/Classes/utils/Log.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d12496528aeb624401211c78041de868", "path": "IosAwnCore/Classes/logs/Logger.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98053a0b35869141356dd411298b6ed747", "path": "IosAwnCore/Classes/managers/LostEventsManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ef32046a09cd662ec3f6cbfdf2a79b0d", "path": "IosAwnCore/Classes/utils/MapUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9842201a0eb2c198b94201e4be11892c1d", "path": "IosAwnCore/Classes/externalLibs/MD5.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988d989718844e07cbd0c2867d69869188", "path": "IosAwnCore/Classes/enumerators/MediaSource.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d142ff2e15564c43e6d4f8b9522d3b46", "path": "IosAwnCore/Classes/utils/MediaUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c3cf70241f36d0592197a0323f0bdc53", "path": "IosAwnCore/Classes/broadcasters/receivers/NotificationActionReceiver.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98aec1dbf2ebcf1c77a1e9a74aae7aa1c9", "path": "IosAwnCore/Classes/extensions/NotificationAttatchment.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98471b55ffe15531d4e7f5819a2e22d3f2", "path": "IosAwnCore/Classes/builders/NotificationBuilder.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fc71a8563851ae2657b44a090771a06b", "path": "IosAwnCore/Classes/models/NotificationButtonModel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9850a8fdf5326a663c9ba2c407c211073e", "path": "IosAwnCore/Classes/models/NotificationCalendarModel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d760d6a85775cadd603147569c9282ab", "path": "IosAwnCore/Classes/models/NotificationChannelModel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d776249d623944976236bafa1de70cee", "path": "IosAwnCore/Classes/models/NotificationContentModel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98263a140fc29232f358cabf976cab7461", "path": "IosAwnCore/Classes/enumerators/NotificationImportance.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f873a822ba49443db38e99ba8e37c5dd", "path": "IosAwnCore/Classes/models/NotificationIntervalModel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9808d18a0a6f732477248f31a5cedfbd87", "path": "IosAwnCore/Classes/enumerators/NotificationLayout.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984c943a6cbf6eaaede73a31eab0786472", "path": "IosAwnCore/Classes/enumerators/NotificationLifeCycle.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e989329d51961f477fe63abb5426e383", "path": "IosAwnCore/Classes/models/NotificationLocalizationModel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9820c4cfbd5a544a169e76832c40cdcab0", "path": "IosAwnCore/Classes/models/NotificationModel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d16116b5324873cf962fa0447cd64ee1", "path": "IosAwnCore/Classes/enumerators/NotificationPermission.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c887213d6c776c72f3f9a42737b11f29", "path": "IosAwnCore/Classes/enumerators/NotificationPrivacy.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980207c14456eed21dae1d8cdc1b7781f4", "path": "IosAwnCore/Classes/models/returnedData/NotificationReceived.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e9b4aa8b08083587acfc83372bb19a79", "path": "IosAwnCore/Classes/models/NotificationScheduleModel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e906d863402c2353d647d4aa4b2f8338", "path": "IosAwnCore/Classes/threads/NotificationSenderAndScheduler.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fa90923c68c242950f7c4ad9bf5f015d", "path": "IosAwnCore/Classes/enumerators/NotificationSource.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9829fac1ae9f0143797905976f5f3d24b6", "path": "IosAwnCore/Classes/managers/PermissionManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98aa80bb4ed319771157aee245b17a310f", "path": "IosAwnCore/Classes/extensions/RealDateTime.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d75fbb955858ce4c930f4ac5ae1d59fd", "path": "IosAwnCore/Classes/broadcasters/receivers/RefreshSchedulesReceiver.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9897290035babc7377fe69d5ae639b6102", "path": "IosAwnCore/Classes/extensions/RegexExtension.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d56b68e947e01d41182c2b55d1a1b81e", "path": "IosAwnCore/Classes/managers/ScheduleManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cfa118bbac5328061249955db79c3efc", "path": "IosAwnCore/Classes/managers/SharedManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987a17b9980aa4e0d191db1c0fa98e4d71", "path": "IosAwnCore/Classes/background/SilentActionRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a6b2e45f443f6029cec1051353d24020", "path": "IosAwnCore/Classes/databases/SQLitePrimitivesDB.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98daf3a4791e84cac872811b8e604dd849", "path": "IosAwnCore/Classes/managers/StatusBarManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a388c65f22ca4cce602e5b6508fec85f", "path": "IosAwnCore/Classes/extensions/StringExtension.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a3d123296eadac71cc1d0638affb37ef", "path": "IosAwnCore/Classes/utils/StringUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984ccaa3a346c7b2d4e2a681f917ea3181", "path": "IosAwnCore/Classes/utils/SwiftUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981fcfa530f1fe5b94e1bb6e0bbf8db875", "path": "IosAwnCore/Classes/externalLibs/SyncronizedArray.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988112b07ccc010f03439f0bd6969c9289", "path": "IosAwnCore/Classes/enumerators/TimeAndDate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98421ba610bab830093ba455578d22b4c9", "path": "IosAwnCore/Classes/utils/TimeZoneUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9887d38911c8c110d2473222b6ff9687ad", "path": "IosAwnCore/Classes/externalLibs/TreeSet.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9851580a3ad68e445bd568f13fe94f09c8", "path": "IosAwnCore/Classes/extensions/UNCrontabNotificationTrigger.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cec716b63f8e70636ad653385e039e43", "path": "IosAwnCore/Classes/extensions/UNMutableNotificationContentExtension.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98998f72e3c0aa616a0f7b19ddaf1d49ce", "path": "IosAwnCore.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98af7bcd46c82a24891d633fd22576dad6", "path": "IosAwnCore-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9877b5dc1e32f90c712c353a00aa7451f6", "path": "IosAwnCore-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9885f14121a88188f1fd514830355d1009", "path": "IosAwnCore-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cc4e3d16b2d9715fc7c26438365b9dfe", "path": "IosAwnCore-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ce7b893aae8e546bc29bd3676d3096d4", "path": "IosAwnCore.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9818f0fc3426e5d05e6708fa32c61b82da", "path": "IosAwnCore.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986e035fa5de4e2e7064a808b07754f52a", "name": "Support Files", "path": "../Target Support Files/IosAwnCore", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9878f222e6bab3b24fc731c0404a4f5a57", "name": "IosAwnCore", "path": "IosAwnCore", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9847cf665bae4dcfbbf678dced88ce6801", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"guid": "bfdfe7dc352907fc980b868725387e9822badc556b242b0419569d60eccda169", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98395c1542d4cb93bff10798da7414cc85", "path": "Pods-Runner.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98f67ea7c224ca5cb4769b1693eaf47890", "path": "Pods-Runner-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98de0835cbdd9733fe079361e7bb75bdea", "path": "Pods-Runner-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9800c340d4cfe86ef7607f068dd965fe76", "path": "Pods-Runner-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98eaf6defe9df231853aea39499ea4554b", "path": "Pods-Runner-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e98d24b3f7a5059b0cb79a429dce23ae484", "path": "Pods-Runner-resources.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987f10f9a8fd182ca775d25699d28e3318", "path": "Pods-Runner-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98da16c4b437e1a5f00cfbe049204d07e4", "path": "Pods-Runner.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98bdce795e5a7f468ffd86ce292e46bf72", "path": "Pods-Runner.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b55a16a846d222fea265f9d10e335496", "path": "Pods-Runner.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9800fbd9ffd264ef0ad1f0afcb485a0b89", "name": "Pods-Runner", "path": "Target Support Files/Pods-Runner", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e983aadeb6c0efc55aa61d4a193c33d1a65", "path": "Pods-RunnerTests.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98a634232c699d5ed3646d3f024c937ffa", "path": "Pods-RunnerTests-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989938b906e3cb2707a2afa9a39150a604", "path": "Pods-RunnerTests-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988d01c1d667722a31ce8e51428338963f", "path": "Pods-RunnerTests-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98a85576fdb8cb73c7cd4dd5902a45a27b", "path": "Pods-RunnerTests-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f448039d0a832e98acdd3ffd87da1731", "path": "Pods-RunnerTests-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98074a9b441078beef16a37aae33ee2900", "path": "Pods-RunnerTests.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e987bd2a5c12d5a72dad789e04e26dc5a25", "path": "Pods-RunnerTests.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98fc46d149385d28a69f8f8bc860e81763", "path": "Pods-RunnerTests.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d5836cf4d97a0c9c99eca09bf2351047", "name": "Pods-RunnerTests", "path": "Target Support Files/Pods-RunnerTests", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f3d357a58233f32f97cf5aa060ebc8be", "name": "Targets Support Files", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98677e601b37074db53aff90e47c8f96d1", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "bfdfe7dc352907fc980b868725387e98", "path": "/Users/<USER>/Desktop/apps/alquranalkareem/ios/Pods/Pods.xcodeproj", "projectDirectory": "/Users/<USER>/Desktop/apps/alquranalkareem/ios/Pods", "targets": ["TARGET@v11_hash=ece423313a5dd598ec17f817e41692a4", "TARGET@v11_hash=2fee73b33bbe5a5121ea861fb5ab72ea", "TARGET@v11_hash=d4c4b86cc46bee108bef7411ad2282bc", "TARGET@v11_hash=7168d10db1850b6fb570283313fd222b", "TARGET@v11_hash=07ebfcb8b444aa27c1fc47d427a70565", "TARGET@v11_hash=a402874d537d16c5b2e1803ed0ab9982", "TARGET@v11_hash=a71d91185c3248d4762bd1a4f28b262d", "TARGET@v11_hash=2e7a644b2a5d53b11a001d3210cc07e7", "TARGET@v11_hash=f4813317694f696bd0e1ec99d492cb7d", "TARGET@v11_hash=ab8f0034b75d3ae3964889476c161a0e", "TARGET@v11_hash=9f5cfab29017d435fd31a0ad0717f2f7", "TARGET@v11_hash=c533b8e4ee341eefc443ac8e7a7971a3", "TARGET@v11_hash=6d4fe8f4841b34940d3e915516151f3f", "TARGET@v11_hash=7e172d961e8bb2525a4deda57b400fc5", "TARGET@v11_hash=81d5ee9a9379ced3390e229363a8ce6c", "TARGET@v11_hash=ac2311eefb4ffe8b9c268df4a5305569", "TARGET@v11_hash=1a6a8078a0b0919194fe08de6da57817", "TARGET@v11_hash=a554b0180ce26cb3caa2ef8d3a7fdb12", "TARGET@v11_hash=939631bc38b7358c707acc0cc283ee40", "TARGET@v11_hash=5de6e81cf082eb129ecbf236b4ef3ec9", "TARGET@v11_hash=7aeb5bcfeb583d4391accea605b32dd3", "TARGET@v11_hash=96d71b050a2b0a513310709d2194ee98", "TARGET@v11_hash=b703479ef7c5dc1ee8d55a8bdac36cb1", "TARGET@v11_hash=f82a90946713985895327162c0f054b4", "TARGET@v11_hash=c93fd75d15bf5a681122b22563c0f960", "TARGET@v11_hash=b4b5573e7d9f9fb136c9a7676214c04e", "TARGET@v11_hash=326cef4f73cb424fa15befaa2955b5d3", "TARGET@v11_hash=b3ee90180859f6e275512ad626061c5f", "TARGET@v11_hash=81574485d53ee2626d9f748642a5d95e", "TARGET@v11_hash=9291a1e86b64652eac091d12410eb98a", "TARGET@v11_hash=bdb32e22ca7e09dd70a7e1b52c3f1b5a", "TARGET@v11_hash=4ea549618185c65d3d4402929054af4e"]}