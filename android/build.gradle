ext {
    compileSdkVersion = 34
    targetSdkVersion = 34
    appCompatVersion = "1.4.2"
}

buildscript {
    ext.kotlin_version = '1.9.0'
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.android.tools.build:gradle:8.7.2'
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven {
            url "${project(':background_fetch').projectDir}/libs"
        }
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
