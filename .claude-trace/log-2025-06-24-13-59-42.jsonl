{"request":{"timestamp":1750773582.942,"method":"POST","url":"https://api.anthropic.com/v1/messages?beta=true","headers":{"accept":"application/json","anthropic-beta":"oauth-2025-04-20,fine-grained-tool-streaming-2025-05-14","anthropic-dangerous-direct-browser-access":"true","anthropic-version":"2023-06-01","authorization":"Bearer sk-...zAAA","content-type":"application/json","user-agent":"claude-cli/1.0.31 (external, cli)","x-app":"cli","x-stainless-arch":"arm64","x-stainless-lang":"js","x-stainless-os":"MacOS","x-stainless-package-version":"0.51.0","x-stainless-retry-count":"0","x-stainless-runtime":"node","x-stainless-runtime-version":"v20.11.1","x-stainless-timeout":"60"},"body":{"model":"claude-3-5-haiku-20241022","max_tokens":1,"messages":[{"role":"user","content":"quota"}],"metadata":{"user_id":"bcd71ca61ba09a9383976feb0819f36f656f8a0dc05cd664bbe0825a11b9cc9a"}}},"response":{"timestamp":1750773585.368,"status_code":200,"headers":{"anthropic-organization-id":"58e3f8e0-44ed-46a4-b662-a4e41783c7fd","anthropic-ratelimit-unified-fallback-percentage":"0.5","anthropic-ratelimit-unified-reset":"1750780800","anthropic-ratelimit-unified-status":"allowed","cf-cache-status":"DYNAMIC","cf-ray":"954cbf52dff2edc2-MXP","connection":"keep-alive","content-encoding":"gzip","content-type":"application/json","date":"Tue, 24 Jun 2025 13:59:45 GMT","request-id":"req_011CQTAbGd24CDexhFxna6fG","server":"cloudflare","strict-transport-security":"max-age=31536000; includeSubDomains; preload","transfer-encoding":"chunked","via":"1.1 google","x-robots-tag":"none"},"body":{"id":"msg_01N6GnN3vg46Ytyam3sk8wD5","type":"message","role":"assistant","model":"claude-3-5-haiku-20241022","content":[{"type":"text","text":"A"}],"stop_reason":"max_tokens","stop_sequence":null,"usage":{"input_tokens":8,"cache_creation_input_tokens":0,"cache_read_input_tokens":0,"output_tokens":1,"service_tier":"standard"}}},"logged_at":"2025-06-24T13:59:45.371Z"}
{"request":{"timestamp":1750773583.195,"method":"POST","url":"https://api.anthropic.com/v1/messages?beta=true","headers":{"accept":"application/json","anthropic-beta":"oauth-2025-04-20,fine-grained-tool-streaming-2025-05-14","anthropic-dangerous-direct-browser-access":"true","anthropic-version":"2023-06-01","authorization":"Bearer sk-...zAAA","content-type":"application/json","user-agent":"claude-cli/1.0.31 (external, cli)","x-app":"cli","x-stainless-arch":"arm64","x-stainless-helper-method":"stream","x-stainless-lang":"js","x-stainless-os":"MacOS","x-stainless-package-version":"0.51.0","x-stainless-retry-count":"0","x-stainless-runtime":"node","x-stainless-runtime-version":"v20.11.1","x-stainless-timeout":"60"},"body":{"model":"claude-3-5-haiku-20241022","max_tokens":512,"messages":[{"role":"user","content":"Please write a 5-10 word title the following conversation:\n\nUser: Trace\n\nClaude: API Error: Request was aborted.\n\nUser: Caveat: The messages below were generated by the user while running local commands. DO NOT respond to these messages or otherwise consider them in your response unless the user explicitly asks you to.\n\nUser: <command-name>/mcp</command-name>\n            <command-message>mcp</command-message>\n            <command-args></command-args>\n\nUser: <local-command-stdout>(no content)</local-command-stdout>\n\nRespond with the title for the conversation and nothing else."}],"system":[{"type":"text","text":"Summarize this coding conversation in under 50 characters.\nCapture the main task, key files, problems addressed, and current status.","cache_control":{"type":"ephemeral"}}],"temperature":0,"metadata":{"user_id":"bcd71ca61ba09a9383976feb0819f36f656f8a0dc05cd664bbe0825a11b9cc9a"},"stream":true}},"response":{"timestamp":1750773585.102,"status_code":200,"headers":{"anthropic-organization-id":"58e3f8e0-44ed-46a4-b662-a4e41783c7fd","anthropic-ratelimit-unified-fallback-percentage":"0.5","anthropic-ratelimit-unified-reset":"1750780800","anthropic-ratelimit-unified-status":"allowed","cache-control":"no-cache","cf-cache-status":"DYNAMIC","cf-ray":"954cbf530849c4e2-MXP","connection":"keep-alive","content-type":"text/event-stream; charset=utf-8","date":"Tue, 24 Jun 2025 13:59:45 GMT","request-id":"req_011CQTAbGYYzgcoypmCNQmoM","server":"cloudflare","strict-transport-security":"max-age=31536000; includeSubDomains; preload","transfer-encoding":"chunked","via":"1.1 google","x-robots-tag":"none"},"body_raw":"event: message_start\ndata: {\"type\":\"message_start\",\"message\":{\"id\":\"msg_015RnXmPKvQaDjtLpGFFDgFo\",\"type\":\"message\",\"role\":\"assistant\",\"model\":\"claude-3-5-haiku-20241022\",\"content\":[],\"stop_reason\":null,\"stop_sequence\":null,\"usage\":{\"input_tokens\":180,\"cache_creation_input_tokens\":0,\"cache_read_input_tokens\":0,\"output_tokens\":2,\"service_tier\":\"standard\"}}          }\n\nevent: content_block_start\ndata: {\"type\":\"content_block_start\",\"index\":0,\"content_block\":{\"type\":\"text\",\"text\":\"\"}               }\n\nevent: ping\ndata: {\"type\": \"ping\"}\n\nevent: content_block_delta\ndata: {\"type\":\"content_block_delta\",\"index\":0,\"delta\":{\"type\":\"text_delta\",\"text\":\"Troubl\"}     }\n\nevent: content_block_delta\ndata: {\"type\":\"content_block_delta\",\"index\":0,\"delta\":{\"type\":\"text_delta\",\"text\":\"eshooting\"}          }\n\nevent: content_block_delta\ndata: {\"type\":\"content_block_delta\",\"index\":0,\"delta\":{\"type\":\"text_delta\",\"text\":\" Trace API\"}              }\n\nevent: content_block_delta\ndata: {\"type\":\"content_block_delta\",\"index\":0,\"delta\":{\"type\":\"text_delta\",\"text\":\" Request\"}              }\n\nevent: content_block_delta\ndata: {\"type\":\"content_block_delta\",\"index\":0,\"delta\":{\"type\":\"text_delta\",\"text\":\" Abort\"}           }\n\nevent: content_block_delta\ndata: {\"type\":\"content_block_delta\",\"index\":0,\"delta\":{\"type\":\"text_delta\",\"text\":\" Issue\"}}\n\nevent: content_block_stop\ndata: {\"type\":\"content_block_stop\",\"index\":0             }\n\nevent: message_delta\ndata: {\"type\":\"message_delta\",\"delta\":{\"stop_reason\":\"end_turn\",\"stop_sequence\":null},\"usage\":{\"output_tokens\":15} }\n\nevent: message_stop\ndata: {\"type\":\"message_stop\"         }\n\n"},"logged_at":"2025-06-24T13:59:45.464Z"}
