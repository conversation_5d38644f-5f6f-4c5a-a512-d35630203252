<svg width="373" height="45" viewBox="0 0 373 45" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Main banner background with gradient -->
  <defs>
    <linearGradient id="bannerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1B5E20;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#2E7D32;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1B5E20;stop-opacity:1" />
    </linearGradient>
    <pattern id="geometricPattern" x="0" y="0" width="30" height="30" patternUnits="userSpaceOnUse">
      <circle cx="15" cy="15" r="2" fill="#ffffff" opacity="0.1"/>
      <path d="M15 5 L25 15 L15 25 L5 15 Z" fill="none" stroke="#ffffff" stroke-width="0.5" opacity="0.2"/>
    </pattern>
  </defs>
  
  <!-- Main banner rectangle with rounded corners -->
  <rect x="35" y="2" width="303" height="41" rx="6" fill="url(#bannerGradient)"/>
  <rect x="35" y="2" width="303" height="41" rx="6" fill="url(#geometricPattern)"/>
  
  <!-- Left ornamental design -->
  <g transform="translate(0, 22.5)">
    <!-- Star pattern -->
    <path d="M20 0 L23.09 9.51 L33 10.98 L26.5 17.49 L28.18 27.49 L20 22.5 L11.82 27.49 L13.5 17.49 L7 10.98 L16.91 9.51 Z" 
          fill="#1B5E20" stroke="#FFD700" stroke-width="1"/>
    <!-- Decorative curves -->
    <path d="M35 -10 Q25 -5 20 0 Q25 5 35 10" fill="none" stroke="#1B5E20" stroke-width="2"/>
    <path d="M35 -15 Q20 -7.5 20 0 Q20 7.5 35 15" fill="none" stroke="#2E7D32" stroke-width="1.5"/>
  </g>
  
  <!-- Right ornamental design (mirror of left) -->
  <g transform="translate(373, 22.5) scale(-1, 1)">
    <!-- Star pattern -->
    <path d="M20 0 L23.09 9.51 L33 10.98 L26.5 17.49 L28.18 27.49 L20 22.5 L11.82 27.49 L13.5 17.49 L7 10.98 L16.91 9.51 Z" 
          fill="#1B5E20" stroke="#FFD700" stroke-width="1"/>
    <!-- Decorative curves -->
    <path d="M35 -10 Q25 -5 20 0 Q25 5 35 10" fill="none" stroke="#1B5E20" stroke-width="2"/>
    <path d="M35 -15 Q20 -7.5 20 0 Q20 7.5 35 15" fill="none" stroke="#2E7D32" stroke-width="1.5"/>
  </g>
  
  <!-- Center decorative elements -->
  <!-- Top center ornament -->
  <g transform="translate(186.5, 2)">
    <circle cx="0" cy="0" r="4" fill="#FFD700" opacity="0.8"/>
    <circle cx="0" cy="0" r="2" fill="#ffffff"/>
  </g>
  
  <!-- Bottom center ornament -->
  <g transform="translate(186.5, 43)">
    <circle cx="0" cy="0" r="4" fill="#FFD700" opacity="0.8"/>
    <circle cx="0" cy="0" r="2" fill="#ffffff"/>
  </g>
  
  <!-- Corner decorations -->
  <!-- Top left -->
  <path d="M40 7 L45 2 L40 2 Z" fill="#FFD700" opacity="0.6"/>
  <path d="M333 7 L328 2 L333 2 Z" fill="#FFD700" opacity="0.6"/>
  
  <!-- Bottom left -->
  <path d="M40 38 L45 43 L40 43 Z" fill="#FFD700" opacity="0.6"/>
  <path d="M333 38 L328 43 L333 43 Z" fill="#FFD700" opacity="0.6"/>
  
  <!-- Inner border decoration -->
  <rect x="45" y="8" width="283" height="29" rx="4" fill="none" stroke="#ffffff" stroke-width="0.5" opacity="0.3"/>
  
  <!-- Side geometric patterns -->
  <!-- Left side -->
  <g transform="translate(55, 22.5)">
    <path d="M0 -5 L5 0 L0 5 L-5 0 Z" fill="#FFD700" opacity="0.4"/>
  </g>
  
  <!-- Right side -->
  <g transform="translate(318, 22.5)">
    <path d="M0 -5 L5 0 L0 5 L-5 0 Z" fill="#FFD700" opacity="0.4"/>
  </g>
  
  <!-- Placeholder for Surah name SVG (centered) -->
  <!-- The actual surah name SVG will be placed here by the application -->
  <g id="surah-name-placeholder" transform="translate(186.5, 22.5)">
    <!-- This group will contain the surah name SVG -->
  </g>
</svg>