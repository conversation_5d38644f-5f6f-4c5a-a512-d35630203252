<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 27.35 27.35">
  <defs>
    <style>
      .cls-1 {
        fill: #eee;
        mix-blend-mode: multiply;
        opacity: .5;
      }

      .cls-2 {
        isolation: isolate;
      }
    </style>
  </defs>
  <g class="cls-2">
    <g id="Layer_2" data-name="Layer 2">
      <g id="OBJECTS">
        <polygon class="cls-1" points="4 23.34 9.67 23.34 13.67 27.35 17.68 23.34 23.34 23.34 23.34 17.68 27.35 13.67 23.34 9.67 23.34 4.01 17.68 4.01 13.67 0 9.67 4.01 4 4.01 4 9.67 0 13.67 4 17.68 4 23.34"/>
      </g>
    </g>
  </g>
</svg>