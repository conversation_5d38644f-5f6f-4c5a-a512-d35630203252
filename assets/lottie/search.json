{"nm": "Search", "ddd": 0, "h": 500, "w": 500, "meta": {"g": "@lottiefiles/toolkit-js 0.33.2"}, "layers": [{"ty": 4, "nm": "Check Middle", "sr": 1, "st": -60, "op": 145, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.134, "y": 1}, "s": [100, 100, 100], "t": 4}, {"s": [69, 69, 100], "t": 16}], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [250, 250, 0], "t": 2, "ti": [33, 0, 0], "to": [-33, 0, 0]}, {"s": [52, 250, 0], "t": 14}], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [100], "t": 2}, {"s": [0], "t": 20}], "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Shape 1", "ix": 1, "cix": 2, "np": 3, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-52.5, 11.5], [-11, 50.5], [60.5, -34.5]]}, "ix": 2}}, {"ty": "st", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Stroke", "nm": "Stroke 1", "lc": 1, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 15, "ix": 5}, "c": {"a": 0, "k": [0.251, 0.298, 0.4314], "ix": 3}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [0, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 1}, {"ty": 4, "nm": "Check Right", "sr": 1, "st": -2, "op": 144, "ip": 58, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [250, 250, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100, "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Shape 1", "ix": 1, "cix": 2, "np": 3, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-52.5, 11.5], [-11, 50.5], [60.5, -34.5]]}, "ix": 2}}, {"ty": "st", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Stroke", "nm": "Stroke 1", "lc": 1, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 15, "ix": 5}, "c": {"a": 0, "k": [0.251, 0.298, 0.4314], "ix": 3}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [0, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "tm", "bm": 0, "hd": false, "mn": "ADBE Vector Filter - Trim", "nm": "Trim Paths 1", "ix": 2, "e": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 58}, {"s": [100], "t": 65}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "s": {"a": 0, "k": 0, "ix": 1}, "m": 1}], "ind": 2}, {"ty": 4, "nm": "Magnifying Glass", "sr": 1, "st": 0, "op": 144, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [86, 86, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-31.335, -31.31, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100, "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 1", "ix": 1, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, -29.547], [29.547, 0], [0, 29.547], [-29.547, 0]], "o": [[0, 29.547], [-29.547, 0], [0, -29.547], [29.547, 0]], "v": [[53.5, 0], [0, 53.5], [-53.5, 0], [0, -53.5]]}, "ix": 2}}, {"ty": "st", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Stroke", "nm": "Stroke 1", "lc": 1, "lj": 1, "ml": 10, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 13, "ix": 5}, "c": {"a": 0, "k": [0.251, 0.298, 0.4314], "ix": 3}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [86, 86], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 3, "parent": 4}, {"ty": 4, "nm": "<PERSON><PERSON>", "sr": 1, "st": 0, "op": 144, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [-30.17, -31.17, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.394, "y": 1}, "s": [648.829, 532.045, 0], "t": 18, "ti": [33.833, 65.833, 0], "to": [-71.333, -60.333, 0]}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [220.829, 170.045, 0], "t": 24, "ti": [0, 0, 0], "to": [0, 0, 0]}, {"o": {"x": 0.216, "y": 0}, "i": {"x": 0, "y": 1}, "s": [220.829, 170.045, 0], "t": 28, "ti": [-17.667, -10.167, 0], "to": [13.667, -11.333, 0]}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [284.829, 168.045, 0], "t": 32, "ti": [0, 0, 0], "to": [0, 0, 0]}, {"o": {"x": 0.375, "y": 0}, "i": {"x": 0.057, "y": 1}, "s": [284.829, 168.045, 0], "t": 34, "ti": [-2.631, -12.748, 0], "to": [1.667, 31.5, 0]}, {"o": {"x": 0.981, "y": 0}, "i": {"x": 0.826, "y": 1}, "s": [216.829, 246.045, 0], "t": 37, "ti": [-9.667, -12, 0], "to": [4.333, 21, 0]}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [280.829, 246.045, 0], "t": 41, "ti": [0, 0, 0], "to": [0, 0, 0]}, {"o": {"x": 0.333, "y": 0}, "i": {"x": 0.04, "y": 1}, "s": [280.829, 246.045, 0], "t": 44, "ti": [-1, -13, 0], "to": [-2.167, 23.667, 0]}, {"o": {"x": 0.871, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [222.829, 322.045, 0], "t": 48, "ti": [-10.667, -0.333, 0], "to": [1, 13, 0]}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [286.829, 324.045, 0], "t": 54, "ti": [0, 0, 0], "to": [0, 0, 0]}, {"o": {"x": 0.923, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [286.829, 324.045, 0], "t": 56, "ti": [-60.667, -35.333, 0], "to": [60.667, 35.333, 0]}, {"s": [650.829, 536.045, 0], "t": 60}], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100, "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 1", "ix": 1, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[1.221, 1.12], [0, 0], [1.122, -1.22], [0, 0], [-1.221, -1.121], [0, 0], [-1.12, 1.221], [0, 0]], "o": [[0, 0], [-1.221, -1.12], [0, 0], [-1.121, 1.221], [0, 0], [1.22, 1.121], [0, 0], [1.121, -1.219]], "v": [[46.352, 42.076], [-23.804, -22.35], [-28.044, -22.169], [-36.836, -12.594], [-36.656, -8.355], [33.501, 56.07], [37.74, 55.889], [46.533, 46.314]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.251, 0.298, 0.4314], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [38.207, 23.72], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 4}, {"ty": 4, "nm": "Profile", "sr": 1, "st": 4, "op": 144, "ip": 9, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [21.129, 20.971, 0], "ix": 1}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0, 0, 100], "t": 9}, {"s": [93.913, 93.913, 100], "t": 15}], "ix": 6, "x": "var $bm_rt;\nvar n, n, t, t, v, amp, freq, decay;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0 && t < 1) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    amp = 0.08;\n    freq = 3;\n    decay = 8;\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [37.904, 43.851, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100, "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Profile", "ix": 1, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[3.642, 0], [0, 0], [0, 3.642], [0, 0], [-3.643, 0], [0, 0], [0, -3.642], [0, 0]], "o": [[0, 0], [-3.643, 0], [0, 0], [0, -3.642], [0, 0], [3.642, 0], [0, 0], [0, 3.642]], "v": [[14.126, 20.72], [-14.126, 20.72], [-20.721, 14.126], [-20.721, -14.126], [-14.126, -20.72], [14.126, -20.72], [20.721, -14.126], [20.721, 14.126]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.8039, 0.6784, 0.502], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [21.129, 20.971], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 5, "parent": 13}, {"ty": 4, "nm": "Line 1", "sr": 1, "st": 5, "op": 144, "ip": 10, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [50.672, 13.223, 0], "ix": 1}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0, 93.883, 100], "t": 10}, {"s": [93.883, 93.883, 100], "t": 16}], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [65.64, 36.578, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 10}, {"s": [100], "t": 16}], "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Line 1", "ix": 1, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[1.657, 0], [0, 0], [0, 1.657], [0, 0], [-1.658, 0], [0, 0], [0, -1.657], [0, 0]], "o": [[0, 0], [-1.658, 0], [0, 0], [0, -1.657], [0, 0], [1.657, 0], [0, 0], [0, 1.657]], "v": [[20.074, 4.963], [-20.074, 4.963], [-23.074, 1.963], [-23.074, -1.963], [-20.074, -4.963], [20.074, -4.963], [23.074, -1.963], [23.074, 1.963]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.8039, 0.6784, 0.502], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [73.746, 13.223], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 6, "parent": 13}, {"ty": 4, "nm": "Line 2", "sr": 1, "st": 5, "op": 144, "ip": 10, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [50.421, 32.288, 0], "ix": 1}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0, 93.883, 100], "t": 11}, {"s": [93.883, 93.883, 100], "t": 17}], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [65.972, 54.476, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 11}, {"s": [100], "t": 17}], "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Line 2", "ix": 1, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[1.657, 0], [0, 0], [0, 1.657], [0, 0], [-1.658, 0], [0, 0], [0, -1.658], [0, 0]], "o": [[0, 0], [-1.658, 0], [0, 0], [0, -1.658], [0, 0], [1.657, 0], [0, 0], [0, 1.657]], "v": [[20.576, 4.962], [-20.576, 4.962], [-23.576, 1.962], [-23.576, -1.962], [-20.576, -4.962], [20.576, -4.962], [23.576, -1.962], [23.576, 1.962]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.8039, 0.6784, 0.502], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [73.996, 32.288], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 7, "parent": 13}, {"ty": 4, "nm": "Line 3", "sr": 1, "st": 5, "op": 144, "ip": 10, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0.501, 60.383, 0], "ix": 1}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0, 93.883, 100], "t": 12}, {"s": [93.883, 93.883, 100], "t": 18}], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [18.539, 80.853, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 12}, {"s": [100], "t": 18}], "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Line 3", "ix": 1, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[1.657, 0], [0, 0], [0, 1.657], [0, 0], [-1.658, 0], [0, 0], [0, -1.657], [0, 0]], "o": [[0, 0], [-1.658, 0], [0, 0], [0, -1.657], [0, 0], [1.657, 0], [0, 0], [0, 1.657]], "v": [[45.159, 4.963], [-45.159, 4.963], [-48.159, 1.963], [-48.159, -1.963], [-45.159, -4.963], [45.159, -4.963], [48.159, -1.963], [48.159, 1.963]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.8039, 0.6784, 0.502], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [48.66, 60.383], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 8, "parent": 13}, {"ty": 4, "nm": "Line 4", "sr": 1, "st": 5, "op": 144, "ip": 10, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0.752, 83.963, 0], "ix": 1}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0, 93.883, 100], "t": 13}, {"s": [93.883, 93.883, 100], "t": 19}], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [18.774, 102.99, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 13}, {"s": [100], "t": 19}], "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Line 4", "ix": 1, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[1.657, 0], [0, 0], [0, 1.657], [0, 0], [-1.658, 0], [0, 0], [0, -1.657], [0, 0]], "o": [[0, 0], [-1.658, 0], [0, 0], [0, -1.657], [0, 0], [1.657, 0], [0, 0], [0, 1.657]], "v": [[45.034, 4.962], [-45.034, 4.962], [-48.034, 1.962], [-48.034, -1.962], [-45.034, -4.962], [45.034, -4.962], [48.034, -1.962], [48.034, 1.962]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.8039, 0.6784, 0.502], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [48.786, 83.963], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 9, "parent": 13}, {"ty": 4, "nm": "Line 5", "sr": 1, "st": 5, "op": 144, "ip": 10, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0.25, 107.543, 0], "ix": 1}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0, 93.883, 100], "t": 14}, {"s": [93.883, 93.883, 100], "t": 20}], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [18.303, 125.128, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 14}, {"s": [100], "t": 20}], "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Line 5", "ix": 1, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[1.657, 0], [0, 0], [0, 1.657], [0, 0], [-1.657, 0], [0, 0], [0, -1.657], [0, 0]], "o": [[0, 0], [-1.657, 0], [0, 0], [0, -1.657], [0, 0], [1.657, 0], [0, 0], [0, 1.657]], "v": [[45.661, 4.962], [-45.661, 4.962], [-48.661, 1.962], [-48.661, -1.962], [-45.661, -4.962], [45.661, -4.962], [48.661, -1.962], [48.661, 1.962]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.8039, 0.6784, 0.502], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [48.911, 107.543], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 10, "parent": 13}, {"ty": 4, "nm": "Line 6", "sr": 1, "st": 5, "op": 144, "ip": 10, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0.25, 131.876, 0], "ix": 1}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0, 93.883, 100], "t": 15}, {"s": [93.883, 93.883, 100], "t": 21}], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [18.303, 147.972, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 15}, {"s": [100], "t": 21}], "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Line 6", "ix": 1, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[1.657, 0], [0, 0], [0, 1.657], [0, 0], [-1.657, 0], [0, 0], [0, -1.656], [0, 0]], "o": [[0, 0], [-1.657, 0], [0, 0], [0, -1.656], [0, 0], [1.657, 0], [0, 0], [0, 1.657]], "v": [[45.661, 4.963], [-45.661, 4.963], [-48.661, 1.963], [-48.661, -1.963], [-45.661, -4.963], [45.661, -4.963], [48.661, -1.963], [48.661, 1.963]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.8039, 0.6784, 0.502], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [48.911, 131.876], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 11, "parent": 13}, {"ty": 4, "nm": "Line 7", "sr": 1, "st": 5, "op": 145, "ip": 10, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0.501, 155.958, 0], "ix": 1}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0, 93.883, 100], "t": 16}, {"s": [93.883, 93.883, 100], "t": 22}], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [18.539, 170.581, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 16}, {"s": [100], "t": 22}], "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Line 7", "ix": 1, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[1.657, 0], [0, 0], [0, 1.656], [0, 0], [-1.658, 0], [0, 0], [0, -1.657], [0, 0]], "o": [[0, 0], [-1.658, 0], [0, 0], [0, -1.657], [0, 0], [1.657, 0], [0, 0], [0, 1.656]], "v": [[45.159, 4.963], [-45.159, 4.963], [-48.159, 1.963], [-48.159, -1.963], [-45.159, -4.963], [45.159, -4.963], [48.159, -1.963], [48.159, 1.963]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.8039, 0.6784, 0.502], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [48.66, 155.958], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 12, "parent": 13}, {"ty": 4, "nm": "Right Page 1", "sr": 1, "st": -6, "op": 145, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [64.304, 101.087, 0], "ix": 1}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100, 94.05, 100], "t": 4}, {"s": [138, 138, 100], "t": 16}], "ix": 6, "x": "var $bm_rt;\nvar n, n, t, t, v, amp, freq, decay;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0 && t < 1) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    amp = 0.05;\n    freq = 2;\n    decay = 8;\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [435.776, 247, 0], "t": 4, "ti": [30.833, -0.003, 0], "to": [-30.833, 0.003, 0]}, {"s": [250.776, 247.017, 0], "t": 16}], "ix": 2, "x": "var $bm_rt;\nvar n, n, t, t, v, amp, freq, decay;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0 && t < 1) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    amp = 0.05;\n    freq = 2;\n    decay = 8;\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100, "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 1", "ix": 1, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[7.048, 0], [0, 0], [0, 7.048], [0, 0], [-7.048, 0], [0, 0], [0, -7.048], [0, 0]], "o": [[0, 0], [-7.048, 0], [0, 0], [0, -7.048], [0, 0], [7.048, 0], [0, 0], [0, 7.048]], "v": [[51.292, 100.837], [-51.293, 100.837], [-64.054, 88.076], [-64.054, -88.076], [-51.293, -100.837], [51.292, -100.837], [64.054, -88.076], [64.054, 88.076]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0.9176, 0.9176, 0.9176], "t": 4}, {"s": [1, 1, 1], "t": 16}], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [101.747, 96.634], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [64.304, 101.087], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 13}, {"ty": 4, "nm": "Right Page 2", "sr": 1, "st": 4, "op": 148, "ip": 4, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [64.304, 101.087, 0], "ix": 1}, "s": {"a": 0, "k": [100, 88.1, 100], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [575.776, 252.017, 0], "t": 8, "ti": [23.333, 0.833, 0], "to": [-23.333, -0.833, 0]}, {"s": [435.776, 247.017, 0], "t": 20}], "ix": 2, "x": "var $bm_rt;\nvar n, n, t, t, v, amp, freq, decay;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0 && t < 1) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    amp = 0.05;\n    freq = 2;\n    decay = 8;\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100, "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 9", "ix": 1, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[7.048, 0], [0, 0], [0, 7.048], [0, 0], [-7.048, 0], [0, 0], [0, -7.047], [0, 0]], "o": [[0, 0], [-7.048, 0], [0, 0], [0, -7.047], [0, 0], [7.048, 0], [0, 0], [0, 7.048]], "v": [[51.292, 100.837], [-51.293, 100.837], [-64.054, 88.076], [-64.054, -88.076], [-51.293, -100.837], [51.292, -100.837], [64.054, -88.076], [64.054, 88.076]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.9176, 0.9176, 0.9176], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [64.304, 101.087], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 14}, {"ty": 4, "nm": "Left Page 1", "sr": 1, "st": 0, "op": 145, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [64.303, 101.087, 0], "ix": 1}, "s": {"a": 0, "k": [100, 93.058, 100], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [64.898, 247.073, 0], "t": -2, "ti": [26.333, 0, 0], "to": [-26.333, 0, 0]}, {"s": [-93.102, 247.073, 0], "t": 10}], "ix": 2, "x": "var $bm_rt;\nvar n, n, t, t, v, amp, freq, decay;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0 && t < 1) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    amp = 0.05;\n    freq = 2;\n    decay = 8;\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100, "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 1", "ix": 1, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[7.048, 0], [0, 0], [0, 7.048], [0, 0], [-7.048, 0], [0, 0], [0, -7.048], [0, 0]], "o": [[0, 0], [-7.048, 0], [0, 0], [0, -7.048], [0, 0], [7.048, 0], [0, 0], [0, 7.048]], "v": [[51.293, 100.837], [-51.292, 100.837], [-64.053, 88.075], [-64.053, -88.076], [-51.292, -100.837], [51.293, -100.837], [64.054, -88.076], [64.054, 88.075]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.9176, 0.9176, 0.9176], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [64.303, 101.087], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 15}, {"ty": 4, "nm": "Middle Text", "sr": 1, "st": 0, "op": 144, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [64.911, 107, 0], "ix": 1}, "s": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [100, 100, 100], "t": 3}, {"s": [75, 75, 100], "t": 15}], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [249.339, 244.683, 0], "t": 2, "ti": [30.667, 0, 0], "to": [-30.667, 0, 0]}, {"s": [65.339, 244.683, 0], "t": 14}], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [100], "t": 2}, {"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [100], "t": 8}, {"s": [0], "t": 14}], "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 1", "ix": 1, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[3.642, 0], [0, 0], [0, 3.642], [0, 0], [-3.642, 0], [0, 0], [0, -3.642], [0, 0]], "o": [[0, 0], [-3.642, 0], [0, 0], [0, -3.642], [0, 0], [3.642, 0], [0, 0], [0, 3.642]], "v": [[20.939, 27.533], [-20.939, 27.533], [-27.534, 20.939], [-27.534, -20.939], [-20.939, -27.533], [20.939, -27.533], [27.534, -20.939], [27.534, 20.939]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.8039, 0.6784, 0.502], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [27.993, 27.784], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 2", "ix": 2, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[1.657, 0], [0, 0], [0, 1.656], [0, 0], [-1.657, 0], [0, 0], [0, -1.658], [0, 0]], "o": [[0, 0], [-1.657, 0], [0, 0], [0, -1.658], [0, 0], [1.657, 0], [0, 0], [0, 1.656]], "v": [[61.661, 6.594], [-61.661, 6.594], [-64.661, 3.594], [-64.661, -3.594], [-61.661, -6.594], [61.661, -6.594], [64.661, -3.594], [64.661, 3.594]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.8039, 0.6784, 0.502], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [64.911, 142.822], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 3", "ix": 3, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[1.657, 0], [0, 0], [0, 1.657], [0, 0], [-1.658, 0], [0, 0], [0, -1.657], [0, 0]], "o": [[0, 0], [-1.658, 0], [0, 0], [0, -1.657], [0, 0], [1.657, 0], [0, 0], [0, 1.657]], "v": [[60.827, 6.594], [-60.827, 6.594], [-63.827, 3.594], [-63.827, -3.594], [-60.827, -6.594], [60.827, -6.594], [63.827, -3.594], [63.827, 3.594]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.8039, 0.6784, 0.502], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [64.744, 111.488], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 4", "ix": 4, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[1.657, 0], [0, 0], [0, 1.657], [0, 0], [-1.658, 0], [0, 0], [0, -1.657], [0, 0]], "o": [[0, 0], [-1.658, 0], [0, 0], [0, -1.657], [0, 0], [1.657, 0], [0, 0], [0, 1.657]], "v": [[28.327, 6.594], [-28.327, 6.594], [-31.327, 3.594], [-31.327, -3.594], [-28.327, -6.594], [28.327, -6.594], [31.327, -3.594], [31.327, 3.594]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.8039, 0.6784, 0.502], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [98.244, 42.822], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 5", "ix": 5, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[1.657, 0], [0, 0], [0, 1.657], [0, 0], [-1.657, 0], [0, 0], [0, -1.657], [0, 0]], "o": [[0, 0], [-1.657, 0], [0, 0], [0, -1.657], [0, 0], [1.657, 0], [0, 0], [0, 1.657]], "v": [[27.661, 6.594], [-27.661, 6.594], [-30.661, 3.594], [-30.661, -3.594], [-27.661, -6.594], [27.661, -6.594], [30.661, -3.594], [30.661, 3.594]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.8039, 0.6784, 0.502], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [97.911, 17.489], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 6", "ix": 6, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[1.657, 0], [0, 0], [0, 1.657], [0, 0], [-1.657, 0], [0, 0], [0, -1.657], [0, 0]], "o": [[0, 0], [-1.657, 0], [0, 0], [0, -1.657], [0, 0], [1.657, 0], [0, 0], [0, 1.657]], "v": [[60.995, 6.594], [-60.995, 6.594], [-63.995, 3.594], [-63.995, -3.594], [-60.995, -6.594], [60.995, -6.594], [63.995, -3.594], [63.995, 3.594]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.8039, 0.6784, 0.502], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [64.577, 80.155], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 7", "ix": 7, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[1.657, 0], [0, 0], [0, 1.657], [0, 0], [-1.657, 0], [0, 0], [0, -1.657], [0, 0]], "o": [[0, 0], [-1.657, 0], [0, 0], [0, -1.657], [0, 0], [1.657, 0], [0, 0], [0, 1.657]], "v": [[61.661, 6.594], [-61.661, 6.594], [-64.661, 3.594], [-64.661, -3.594], [-61.661, -6.594], [61.661, -6.594], [64.661, -3.594], [64.661, 3.594]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.8039, 0.6784, 0.502], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [64.911, 175.155], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 8", "ix": 8, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[1.657, 0], [0, 0], [0, 1.657], [0, 0], [-1.657, 0], [0, 0], [0, -1.657], [0, 0]], "o": [[0, 0], [-1.657, 0], [0, 0], [0, -1.657], [0, 0], [1.657, 0], [0, 0], [0, 1.657]], "v": [[60.995, 6.594], [-60.995, 6.594], [-63.995, 3.594], [-63.995, -3.594], [-60.995, -6.594], [60.995, -6.594], [63.995, -3.594], [63.995, 3.594]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.8039, 0.6784, 0.502], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [64.577, 207.155], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 16}, {"ty": 4, "nm": "<PERSON> Page", "sr": 1, "st": 0, "op": 144, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [91.789, 134.239, 0], "ix": 1}, "s": {"a": 1, "k": [{"o": {"x": 0.392, "y": 0}, "i": {"x": 0.598, "y": 1}, "s": [100, 100, 100], "t": 3}, {"s": [69, 69, 100], "t": 15}], "ix": 6, "x": "var $bm_rt;\nvar n, n, t, t, v, amp, freq, decay;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0 && t < 1) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    amp = 0.05;\n    freq = 2;\n    decay = 8;\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.377, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [227.461, 246.517, 0], "t": 2, "ti": [30.167, 0, 0], "to": [-30.167, 0, 0]}, {"s": [46.461, 246.517, 0], "t": 14}], "ix": 2, "x": "var $bm_rt;\nvar n, n, t, t, v, amp, freq, decay;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0 && t < 1) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    amp = 0.05;\n    freq = 2;\n    decay = 8;\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100, "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 1", "ix": 1, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[7.048, 0], [0, 0], [0, 7.048], [0, 0], [-7.047, 0], [0, 0], [0, -7.048], [0, 0]], "o": [[0, 0], [-7.047, 0], [0, 0], [0, -7.048], [0, 0], [7.048, 0], [0, 0], [0, 7.048]], "v": [[78.779, 133.989], [-78.779, 133.989], [-91.539, 121.227], [-91.539, -121.228], [-78.779, -133.989], [78.779, -133.989], [91.539, -121.228], [91.539, 121.227]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 2", "c": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [1, 1, 1], "t": 2}, {"s": [0.9176, 0.9176, 0.9176], "t": 14}], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [115.102, 134.239], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 17}], "v": "5.4.4", "fr": 24, "op": 72, "ip": 0, "assets": []}