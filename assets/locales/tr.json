{"AH": "للهجرة", "@AH": {"type": "text", "placeholders": {}}, "AI Insights": "Yapay Zeka Görüşleri", "@AI Insights": {"type": "text", "placeholders": {}}, "Admin Panel": "Yönetici Paneli", "@Admin Panel": {"type": "text", "placeholders": {}}, "Asr": "العصر", "@Asr": {"type": "text", "placeholders": {}}, "Day": "يوم", "@Day": {"type": "text", "placeholders": {}}, "Days": "أيام", "@Days": {"type": "text", "placeholders": {}}, "Dhu Al-Hijjah": "ذوالحجة", "@Dhu Al-Hijjah": {"type": "text", "placeholders": {}}, "Dhu Al-Qi'dah": "ذو القعدة", "@Dhu Al-Qi'dah": {"type": "text", "placeholders": {}}, "Dhuhr": "الظهر", "@Dhuhr": {"type": "text", "placeholders": {}}, "EidAl-Adha": "<PERSON><PERSON><PERSON> الأ<PERSON><PERSON>ى", "@EidAl-Adha": {"type": "text", "placeholders": {}}, "EidAl-Fitr": "<PERSON><PERSON><PERSON> الفطر", "@EidAl-Fitr": {"type": "text", "placeholders": {}}, "Fajr": "الفجر", "@Fajr": {"type": "text", "placeholders": {}}, "Fri": "الجمعة", "Friday": "الجمعة", "@Friday": {"type": "text", "placeholders": {}}, "Isha": "العشاء", "@Isha": {"type": "text", "placeholders": {}}, "Jumada Al-Awwal": "ج<PERSON><PERSON><PERSON> الاول", "@Jumada Al-Awwal": {"type": "text", "placeholders": {}}, "Jumada Al-Thani": "جمادى الآخرة", "@Jumada Al-Thani": {"type": "text", "placeholders": {}}, "Last Third": "الثلث الأخير", "@Last Third": {"type": "text", "placeholders": {}}, "Maghrib": "المغرب", "@Maghrib": {"type": "text", "placeholders": {}}, "Meccan": "مكية", "@Meccan": {"type": "text", "placeholders": {}}, "Medinan": "مدنية", "@Medinan": {"type": "text", "placeholders": {}}, "Middle of the Night": "منت<PERSON><PERSON> الليل", "@Middle of the Night": {"type": "text", "placeholders": {}}, "Mon": "الإثنين", "Monday": "الأثنين", "@Monday": {"type": "text", "placeholders": {}}, "Muharram": "م<PERSON><PERSON><PERSON>", "@Muharram": {"type": "text", "placeholders": {}}, "Rabi' Al-Awwal": "ربيع الاول", "@Rabi' Al-Awwal": {"type": "text", "placeholders": {}}, "Rabi' Al-Thani": "ربيع الثاني", "@Rabi' Al-Thani": {"type": "text", "placeholders": {}}, "Rajab": "ر<PERSON><PERSON>", "@Rajab": {"type": "text", "placeholders": {}}, "Ramadan": "رمضان", "@Ramadan": {"type": "text", "placeholders": {}}, "RemainsUntilTheEndOf": "تبقى على إنتهاء", "@RemainsUntilTheEndOf": {"type": "text", "placeholders": {}}, "Safar": "صفر", "@Safar": {"type": "text", "placeholders": {}}, "Sat": "السبت", "Saturday": "السبت", "@Saturday": {"type": "text", "placeholders": {}}, "Sha'aban": "شعبان", "@Sha'aban": {"type": "text", "placeholders": {}}, "Shawwal": "شوال", "@Shawwal": {"type": "text", "placeholders": {}}, "Sun": "ال<PERSON><PERSON>د", "Sunday": "ال<PERSON><PERSON>د", "@Sunday": {"type": "text", "placeholders": {}}, "Sunrise": "الشروق", "@Sunrise": {"type": "text", "placeholders": {}}, "Thu": "الخميس", "Thursday": "الخميس", "@Thursday": {"type": "text", "placeholders": {}}, "Tue": "الثلاثاء", "Tuesday": "الثلاثاء", "@Tuesday": {"type": "text", "placeholders": {}}, "Wed": "الأربعاء", "Wednesday": "الأربعاء", "@Wednesday": {"type": "text", "placeholders": {}}, "What's New": "ما الجديد", "@What's New": {"type": "text", "placeholders": {}}, "What'sNewDetails10": "۞ <PERSON>y<PERSON><PERSON><PERSON>, Türkçe ve Rusça dillerini desteklemeye başladı.\n۞ Daha fazla okuyucu eklendi.", "@What'sNewDetails10": {"type": "text", "placeholders": {}}, "What'sNewTitle": "Yeni ve kullanıcı dostu arayüz:", "@What'sNewTitle": {"type": "text", "placeholders": {}}, "aboutApp": "Uygulama hakkında", "@aboutApp": {"type": "text", "placeholders": {}}, "aboutBook": "عن الكتاب", "@aboutBook": {"type": "text", "placeholders": {}}, "aboutSurah": "عن السورة", "@aboutSurah": {"type": "text", "placeholders": {}}, "about_app": "تطبيق \"القرآن الكريم - مكتبة الحكمة\" هو تطبيق متكامل يقدم مجموعة واسعة من المزايا لتعزيز تجربة قراءة وتعلم القرآن الكريم، ومن هذه المميزات:", "@about_app": {"type": "text", "placeholders": {}}, "about_app2": "من اهم مميزات البرنامج :", "@about_app2": {"type": "text", "placeholders": {}}, "about_app3": "۞ واجهة جديدة وسهلة الاستخدام: تم تصميم التطبيق بواجهة مستخدم عصرية وبديهية تسهل على المستخدمين التنقل والوصول إلى المزايا المختلفة.\n۞ اعتماد طبعة مجمع الملك فهد: يستخدم التطبيق طبعة مجمع الملك فهد لطباعة المصحف الشريف، المعروفة بموثوقيتها وإتقانها، مما يضمن صحة النص ودقته.\n۞ قراءة تفاعلية ومتكاملة: يوفر التطبيق إمكانية القراءة التفاعلية مع نص المصحف الإلكتروني، بالإضافة إلى الاستماع للتلاوات، ودراسة وحفظ القرآن الكريم بسهولة.\n۞ قراءة مرنة: يمكن للمستخدمين قراءة القرآن كما لو كانوا يقرؤون المصحف الورقي أو اختيار وضع الآية المنفردة لتركيز أكبر.\n۞ خاصية البحث النصي: يحتوي التطبيق على ميزة البحث الفوري في آيات القرآن، مع إمكانية الانتقال مباشرة إلى الصفحة أو السورة المطلوبة.\n۞ إضافة شارات مرجعية: تسمح هذه الميزة بحفظ الصفحات أو الآيات للرجوع إليها بسهولة في أي وقت.\n۞ الاستماع للآيات: يوفر التطبيق إمكانية الاستماع لكل آية بصوت عدد من القراء المشهورين.\n۞ تفسير وترجمة الآيات: يمكن للمستخدمين الوصول إلى التفسير أو الترجمة لكل آية، والتغيير بين التفاسير بحسب الرغبة.\n۞ التنقل السهل بين السور: يتيح التطبيق التنقل بين السور بطريقة سلسة وسريعة.\n۞ قراءة علامات الوقف: تساعد هذه الميزة على فهم أماكن الوقف المناسبة أثناء القراءة.\n۞ أذكار حصن المسلم: يمكن قراءة أذكار حصن المسلم كاملًا والتنقل بين الأقسام بسهولة، مع إمكانية إضافة أذكار إلى المفضلة.\n۞ تغيير أنماط الألوان: يدعم التطبيق تغيير أنماط الألوان، بما في ذلك النمط الداكن، لتحسين تجربة القراءة.\n۞الاستماع والتحميل: يمكن الاستماع إلى السور أو تحميلها للاستماع في وقت لاحق دون الحاجة إلى الإنترنت.\n\nهذه المميزات تجعل تطبيق \"القرآن الكريم - مكتبة الحكمة\" أداة شاملة لكل من يرغب في قراءة، تعلم، وتدبر القرآن الكريم بطريقة ميسرة وفعالة.", "@about_app3": {"type": "text", "placeholders": {}}, "about_us": "Uygulama hakkında", "@about_us": {"type": "text", "placeholders": {}}, "account_created_synced": "Hesap oluşturuldu! Verileriniz artık bulutla senkronize edildi.", "@account_created_synced": {"type": "text", "placeholders": {}}, "account_created_verify_email": "Hesap oluşturuldu! Doğrulamak için lütfen e-postanızı kontrol edin.", "@account_created_verify_email": {"type": "text", "placeholders": {}}, "addBookmark": "تم إضافة الفاصلة", "@addBookmark": {"type": "text", "placeholders": {}}, "addKhatmah": "<PERSON><PERSON><PERSON>", "@addKhatmah": {"type": "text", "placeholders": {}}, "addMore": "<PERSON><PERSON><PERSON> المزيد", "@addMore": {"type": "text", "placeholders": {}}, "addReminder": "إضافة تذكير", "@addReminder": {"type": "text", "placeholders": {}}, "addToBookmark": "Yer imine ekle", "@addToBookmark": {"type": "text", "placeholders": {}}, "addZekrBookmark": "تم إضافة الذكر إلى المفضلة", "@addZekrBookmark": {"type": "text", "placeholders": {}}, "add_new_bookmark": "Yer imi ekle", "@add_new_bookmark": {"type": "text", "placeholders": {}}, "add_new_note": "Yeni not ekle", "@add_new_note": {"type": "text", "placeholders": {}}, "agree_terms": "Hizmet Şartlarını ve Gizlilik Politikasını kabul ediyorum", "@agree_terms": {"type": "text", "placeholders": {}}, "ai_configuration": "Yapay Zeka Yapılandırması", "@ai_configuration": {"type": "text", "placeholders": {}}, "ai_error": "Yapay zeka görüşlerini yüklerken hata", "@ai_error": {"type": "text", "placeholders": {}}, "ai_insights_for_verse": "Ayet i<PERSON><PERSON> Zeka Görüşleri", "@ai_insights_for_verse": {"type": "text", "placeholders": {}}, "alheekmahlib": "مكتبة الحكمة", "@alheekmahlib": {"type": "text", "placeholders": {}}, "allBooks": "كل الكتب", "@allBooks": {"type": "text", "placeholders": {}}, "allJuz": "الأجزاء", "@allJuz": {"type": "text", "placeholders": {}}, "already_have_account": "Zaten hesabınız var mı?", "@already_have_account": {"type": "text", "placeholders": {}}, "appLang": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@appLang": {"type": "text", "placeholders": {}}, "appName": "Quranic Insights", "@appName": {"description": "appName", "type": "text", "placeholders": {}}, "arafah": "يوم عرفه", "@arafah": {"type": "text", "placeholders": {}}, "arafahReminder": "تذكير بصيام يوم عرفة", "@arafahReminder": {"type": "text", "placeholders": {}}, "ashura": "عاشوراء", "@ashura": {"type": "text", "placeholders": {}}, "aya_count": "Ayet sayısı", "@aya_count": {"type": "text", "placeholders": {}}, "ayahs": "Ayetler", "@ayahs": {"type": "text", "placeholders": {}}, "azkar": "Z<PERSON>rler", "@azkar": {"type": "text", "placeholders": {}}, "azkarfav": "<PERSON><PERSON><PERSON>", "@azkarfav": {"type": "text", "placeholders": {}}, "backTo": "رجوع للـ", "@backTo": {"type": "text", "placeholders": {}}, "blueMode": "أزرق", "@blueMode": {"type": "text", "placeholders": {}}, "bookmark": "الفواصل", "@bookmark": {"type": "text", "placeholders": {}}, "bookmark_title": "<PERSON>r imi adı", "@bookmark_title": {"type": "text", "placeholders": {}}, "bookmarks": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "@bookmarks": {"type": "text", "placeholders": {}}, "bookmarksList": "قائمة الفواصل", "@bookmarksList": {"type": "text", "placeholders": {}}, "booksDeleted": "تم حذف الكتاب.", "@booksDeleted": {"type": "text", "placeholders": {}}, "booksDownloaded": "تم تحميل الكتاب.", "@booksDownloaded": {"type": "text", "placeholders": {}}, "brown": "<PERSON><PERSON><PERSON><PERSON><PERSON> tema", "@brown": {"type": "text", "placeholders": {}}, "brownMode": "بني", "@brownMode": {"type": "text", "placeholders": {}}, "calendar": "التقويم", "calenderSettings": "إعدادات التقويم", "@calenderSettings": {"type": "text", "placeholders": {}}, "cancel": "İptal", "@cancel": {"type": "text", "placeholders": {}}, "chapterBook": "أبو<PERSON><PERSON> الكتاب", "@chapterBook": {"type": "text", "placeholders": {}}, "choiceAyah": "يرجى اختيار الآية أولًا!", "@choiceAyah": {"type": "text", "placeholders": {}}, "choiceBackgroundColor": "اختر لون الخلفية", "@choiceBackgroundColor": {"type": "text", "placeholders": {}}, "choiceColor": "أ<PERSON><PERSON><PERSON> اللون", "@choiceColor": {"type": "text", "placeholders": {}}, "chooseDhekr": "اخت<PERSON> الذكر", "@chooseDhekr": {"type": "text", "placeholders": {}}, "choseQuran": "اختر طريقة القراءة", "@choseQuran": {"type": "text", "placeholders": {}}, "close": "Ka<PERSON><PERSON>", "@close": {"type": "text", "placeholders": {}}, "confirm_password": "<PERSON><PERSON><PERSON><PERSON>", "@confirm_password": {"type": "text", "placeholders": {}}, "continue_with_apple": "Apple ile devam et", "@continue_with_apple": {"type": "text", "placeholders": {}}, "continue_with_google": "Google ile devam et", "@continue_with_google": {"type": "text", "placeholders": {}}, "continue_without_account": "<PERSON><PERSON><PERSON> o<PERSON> de<PERSON> et", "@continue_without_account": {"type": "text", "placeholders": {}}, "copy": "Kopyala", "@copy": {"type": "text", "placeholders": {}}, "copyAyah": "تم نسخ الآية", "@copyAyah": {"type": "text", "placeholders": {}}, "copyAzkarText": "تم نسخ الذكر", "@copyAzkarText": {"type": "text", "placeholders": {}}, "copyTafseer": "تم نسخ التفسير", "@copyTafseer": {"type": "text", "placeholders": {}}, "createKhatmah": "<PERSON><PERSON><PERSON><PERSON> ختمة", "@createKhatmah": {"type": "text", "placeholders": {}}, "createPlayList": "أنشئ قائمة التشغيل", "@createPlayList": {"type": "text", "placeholders": {}}, "create_account": "<PERSON><PERSON><PERSON>", "@create_account": {"type": "text", "placeholders": {}}, "create_account_and_sync": "<PERSON><PERSON>p <PERSON> ve Senkronize Et", "@create_account_and_sync": {"type": "text", "placeholders": {}}, "create_account_to_sync": "<PERSON>r işaretlerinizi ve ilerlemenizi tüm cihazlarınızda senkronize etmek için bir hesap o<PERSON>ştur<PERSON>", "@create_account_to_sync": {"type": "text", "placeholders": {}}, "customReminder": "تذكير مخصص", "@customReminder": {"type": "text", "placeholders": {}}, "dailyZeker": "<PERSON><PERSON><PERSON><PERSON><PERSON>ı", "@dailyZeker": {"type": "text", "placeholders": {}}, "dark": "Karanlık tema", "@dark": {"type": "text", "placeholders": {}}, "darkMode": "الداكن", "@darkMode": {"type": "text", "placeholders": {}}, "defaultFontText": "الخط الافتراضي", "@defaultFontText": {"type": "text", "placeholders": {}}, "delete": "Sil", "@delete": {"type": "text", "placeholders": {}}, "deletedBookmark": "تم حذف الفاصلة!", "@deletedBookmark": {"type": "text", "placeholders": {}}, "deletedPlayList": "تم حذف قائمة التشغيل", "@deletedPlayList": {"type": "text", "placeholders": {}}, "deletedReminder": "تم حذف التذكير!", "@deletedReminder": {"type": "text", "placeholders": {}}, "deletedZekrBookmark": "تم حذف الذكر من المفضلة!", "@deletedZekrBookmark": {"type": "text", "placeholders": {}}, "divisionBySahabah": "تحزيب الصحابة", "@divisionBySahabah": {"type": "text", "placeholders": {}}, "dont_have_account": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>z yok mu?", "@dont_have_account": {"type": "text", "placeholders": {}}, "download": "تحميل", "@download": {"type": "text", "placeholders": {}}, "downloadBookFirst": "يرجى تحميل الكتاب أولًا.", "@downloadBookFirst": {"type": "text", "placeholders": {}}, "downloadedFontsText": "<PERSON><PERSON> المصحف", "@downloadedFontsText": {"type": "text", "placeholders": {}}, "downloading": "جارِ التحميل...", "@downloading": {"type": "text", "placeholders": {}}, "duration": "المدة", "@duration": {"type": "text", "placeholders": {}}, "edit": "<PERSON><PERSON><PERSON><PERSON>", "@edit": {"type": "text", "placeholders": {}}, "editHijriDay": "تعديل الأيام", "@editHijriDay": {"type": "text", "placeholders": {}}, "eidGreetingContent": "تقبل الله منا ومنكم الصيام والقيام وصالح الأعمال.", "@eidGreetingContent": {"type": "text", "placeholders": {}}, "eidGreetingContent2": "كل عام وأنتم إلى الله أقرب.", "@eidGreetingContent2": {"type": "text", "placeholders": {}}, "eidGreetingTitle": "<PERSON><PERSON>", "@eidGreetingTitle": {"type": "text", "placeholders": {}}, "email": "Bizimle iletişime geçin", "@email": {"type": "text", "placeholders": {}}, "enable_cloud_sync": "Bulut Senkronizasyonunu <PERSON>r", "@enable_cloud_sync": {"type": "text", "placeholders": {}}, "events": "المناسبات", "facebook": "تابعنا على فيسبوك", "@facebook": {"type": "text", "placeholders": {}}, "fillAllFields": "يرجى ملء جميع الخانات!", "@fillAllFields": {"type": "text", "placeholders": {}}, "fontSize": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "@fontSize": {"type": "text", "placeholders": {}}, "fonts": "الخطوط", "@fonts": {"type": "text", "placeholders": {}}, "fontsNotes": "للحصول على تجربة أفضل ومظهر مثالي للمصحف، يرجى تحميل الخطوط الخاصة به.", "@fontsNotes": {"type": "text", "placeholders": {}}, "forgot_password": "<PERSON>if<PERSON>i <PERSON>uttum?", "@forgot_password": {"type": "text", "placeholders": {}}, "from": "من", "@from": {"type": "text", "placeholders": {}}, "full_name": "Tam Ad", "@full_name": {"type": "text", "placeholders": {}}, "generate_insights": "Görüş Oluştur", "@generate_insights": {"type": "text", "placeholders": {}}, "generating": "Oluşturuluyor...", "@generating": {"type": "text", "placeholders": {}}, "green": "<PERSON><PERSON><PERSON>", "@green": {"type": "text", "placeholders": {}}, "hasPassed": "قد <PERSON>ضى", "@hasPassed": {"type": "text", "placeholders": {}}, "hijri": "هجري", "hijriCalendar": "التقويم الهجري", "hijriNote": "تنويه: يرجى العلم بأنه قد يحدث اختلاف طفيف في الأوقات المحددة نظرًا لأن التقويم الهجري يستند إلى الرؤية المباشرة للهلال. نحن نبذل قصارى جهدنا لضمان دقة التواريخ المعلنة، مع الأخذ في الاعتبار انه سيتم تصحيح التقويم في حال رؤية الهلال.", "@hijriNote": {"type": "text", "placeholders": {}}, "hizb": "الحزب", "@hizb": {"type": "text", "placeholders": {}}, "home": "الرئيسية", "@home": {"type": "text", "placeholders": {}}, "islamicCalendar": "التقويم الاسلامي", "juz": "Cüz", "@juz": {"type": "text", "placeholders": {}}, "khatmah": "الختمة", "@khatmah": {"type": "text", "placeholders": {}}, "khatmahName": "إسم الختمة", "@khatmahName": {"type": "text", "placeholders": {}}, "lang": "<PERSON><PERSON><PERSON>", "@lang": {"type": "text", "placeholders": {}}, "langChange": "<PERSON><PERSON>", "@langChange": {"type": "text", "placeholders": {}}, "lastDayOf": "اليوم الأخير لشهر", "@lastDayOf": {"type": "text", "placeholders": {}}, "lastListen": "آخر إستماع", "@lastListen": {"type": "text", "placeholders": {}}, "lastRead": "آخر قراءة", "@lastRead": {"type": "text", "placeholders": {}}, "lastSearch": "<PERSON><PERSON><PERSON> بحث", "@lastSearch": {"type": "text", "placeholders": {}}, "loading_ai": "Yapay zeka görüşleri yükleniyor...", "@loading_ai": {"type": "text", "placeholders": {}}, "menu": "<PERSON><PERSON>", "@menu": {"description": "menu", "type": "text", "placeholders": {}}, "mobileDataAyat": "تنويه: أنت تستخدم البيانات الخلوية في تحميل الايآت!", "@mobileDataAyat": {"type": "text", "placeholders": {}}, "mobileDataListen": "تنويه: أنت تستخدم البيانات الخلوية للإستماع للسور!", "@mobileDataListen": {"type": "text", "placeholders": {}}, "mobileDataSurahs": "تنويه: أنت تستخدم البيانات الخلوية في تحميل السور!", "@mobileDataSurahs": {"type": "text", "placeholders": {}}, "month": "شهر", "myLibrary": "مكتبتي", "@myLibrary": {"type": "text", "placeholders": {}}, "next": "التالي", "@next": {"type": "text", "placeholders": {}}, "nightOfQadir": "تذكير بليلة القدر", "@nightOfQadir": {"type": "text", "placeholders": {}}, "noBooksDownloaded": "لم تحمل أي كتاب بعد.", "@noBooksDownloaded": {"type": "text", "placeholders": {}}, "noInternet": "الجهاز غير متصل بالانترنت!", "@noInternet": {"type": "text", "placeholders": {}}, "noNotifications": "لا توجد إشعارات", "@noNotifications": {"type": "text", "placeholders": {}}, "note_details": "Notun ayrıntıları", "@note_details": {"type": "text", "placeholders": {}}, "note_title": "Başlık", "@note_title": {"description": "note_title", "type": "text", "placeholders": {}}, "notes": "Notlar", "@notes": {"description": "notes", "type": "text", "placeholders": {}}, "notification": "الإشعارات", "@notification": {"type": "text", "placeholders": {}}, "notifyAdhkarBody": "لا تنسى قراءة @adhkarType", "@notifyAdhkarBody": {"type": "text", "placeholders": {"adhkarType": {}}}, "notifyBooksBody": "لا تنسى متابعة قراءة كتاب @bookName", "@notifyBooksBody": {"type": "text", "placeholders": {"bookName": {}}}, "notifyListenBody": "لا تنسى متابعة الإستماع للسور", "@notifyListenBody": {"type": "text", "placeholders": {}}, "notifyQuranBody": "لقد توقفت عند الصفحة @currentPageNumber في القرآن الكريم، هل ترغب في المتابعة؟", "@notifyQuranBody": {"type": "text", "placeholders": {"currentPageNumber": {}}}, "ok": "<PERSON><PERSON>", "@ok": {"type": "text", "placeholders": {}}, "oldMode": "القديم", "@oldMode": {"type": "text", "placeholders": {}}, "onboardDesc1": "- سهولة البحث عن آية.\n- تغيير اللغة.\n- الإستماع للصفحة.\n- تغيير القارء.", "@onboardDesc1": {"type": "text", "placeholders": {}}, "onboardDesc2": "يمكن قراءة التفسير لكل آية عن طريق سحب القائمة إلى الأعلى.", "@onboardDesc2": {"type": "text", "placeholders": {}}, "onboardDesc3": "1- عند النقر مرتين يتم تكبير الصفحة.\n2- عند النقر المطول يظهر لك خيار حفظ الصفحة.\n3- عند الضغط مرة واحدة تظهر لك القوائم.", "@onboardDesc3": {"type": "text", "placeholders": {}}, "onboardTitle1": "Kullanımı kolay arayüz", "@onboardTitle1": {"type": "text", "placeholders": {}}, "onboardTitle2": "<PERSON><PERSON><PERSON> gö<PERSON>", "@onboardTitle2": {"type": "text", "placeholders": {}}, "onboardTitle3": "<PERSON><PERSON><PERSON><PERSON>", "@onboardTitle3": {"type": "text", "placeholders": {}}, "online": "اونلاين", "@online": {"type": "text", "placeholders": {}}, "or": "VEYA", "@or": {"type": "text", "placeholders": {}}, "ourApps": "تطبيقاتنا", "@ourApps": {"type": "text", "placeholders": {}}, "page": "Say<PERSON>", "@page": {"type": "text", "placeholders": {}}, "pageNo": "رقم الصفحة", "@pageNo": {"type": "text", "placeholders": {}}, "pages": "<PERSON><PERSON><PERSON>", "@pages": {"type": "text", "placeholders": {}}, "part": "Cüz", "@part": {"type": "text", "placeholders": {}}, "password": "Şifre", "@password": {"type": "text", "placeholders": {}}, "password_min_length": "Şifre en az 6 karakter olmalıdır", "@password_min_length": {"type": "text", "placeholders": {}}, "passwords_dont_match": "<PERSON><PERSON><PERSON><PERSON> eşleşmiyor", "@passwords_dont_match": {"type": "text", "placeholders": {}}, "pauseSurah": "<PERSON><PERSON> du<PERSON>", "@pauseSurah": {"type": "text", "placeholders": {}}, "pending_validation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@pending_validation": {"type": "text", "placeholders": {}}, "playList": "قوائم التشغيل", "@playList": {"type": "text", "placeholders": {}}, "playListName": "اكتب إسم القائمة", "@playListName": {"type": "text", "placeholders": {}}, "please_agree_terms": "Lütfen şartları ve koşulları kabul edin", "@please_agree_terms": {"type": "text", "placeholders": {}}, "please_confirm_password": "Lütfen şifrenizi onaylayın", "@please_confirm_password": {"type": "text", "placeholders": {}}, "please_enter_email": "Lütfen e-postanızı girin", "@please_enter_email": {"type": "text", "placeholders": {}}, "please_enter_name": "Lütfen adınızı girin", "@please_enter_name": {"type": "text", "placeholders": {}}, "please_enter_password": "Lütfen şifrenizi girin", "@please_enter_password": {"type": "text", "placeholders": {}}, "please_enter_valid_email": "Lütfen geçerli bir e-posta girin", "@please_enter_valid_email": {"type": "text", "placeholders": {}}, "qibla": "<PERSON><PERSON><PERSON> yönü", "@qibla": {"type": "text", "placeholders": {}}, "quran": "Kur'an-<PERSON> Kerim", "@quran": {"type": "text", "placeholders": {}}, "quranAudio": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dinle", "@quranAudio": {"type": "text", "placeholders": {}}, "quranPages": "القرآن (صفحات)", "@quranPages": {"type": "text", "placeholders": {}}, "quranText": "القرآن (آيات)", "@quranText": {"type": "text", "placeholders": {}}, "quran_sorah": "<PERSON><PERSON>", "@quran_sorah": {"type": "text", "placeholders": {}}, "ramadhan": "شهر رمضان", "@ramadhan": {"type": "text", "placeholders": {}}, "ramadhanMubarak": "<PERSON><PERSON><PERSON>", "@ramadhanMubarak": {"type": "text", "placeholders": {}}, "readLess": "أق<PERSON><PERSON> أقل", "@readLess": {"type": "text", "placeholders": {}}, "readMore": "أقر<PERSON> المزيد", "@readMore": {"type": "text", "placeholders": {}}, "reader1": "<PERSON><PERSON><PERSON> البا<PERSON>ط", "@reader1": {"type": "text", "placeholders": {}}, "reader10": "قادر الكردي", "@reader10": {"type": "text", "placeholders": {}}, "reader11": "شيرزاد طاهر", "@reader11": {"type": "text", "placeholders": {}}, "reader12": "عبدالرحمن العوسي", "@reader12": {"type": "text", "placeholders": {}}, "reader13": "وديع اليمني", "@reader13": {"type": "text", "placeholders": {}}, "reader14": "ياسر الدوسري", "@reader14": {"type": "text", "placeholders": {}}, "reader15": "ع<PERSON><PERSON> الله الجهني", "@reader15": {"type": "text", "placeholders": {}}, "reader16": "فارس عباد", "@reader16": {"type": "text", "placeholders": {}}, "reader17": "<PERSON><PERSON><PERSON>", "@reader17": {"type": "text", "placeholders": {}}, "reader18": "<PERSON><PERSON>", "@reader18": {"type": "text", "placeholders": {}}, "reader19": "<PERSON>", "@reader19": {"type": "text", "placeholders": {}}, "reader2": "م<PERSON>م<PERSON> المنشاوي", "@reader2": {"type": "text", "placeholders": {}}, "reader20": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "@reader20": {"type": "text", "placeholders": {}}, "reader3": "محمود الحصري", "@reader3": {"type": "text", "placeholders": {}}, "reader4": "<PERSON><PERSON><PERSON><PERSON> العجمي", "@reader4": {"type": "text", "placeholders": {}}, "reader5": "ماهر المعيقلي", "@reader5": {"type": "text", "placeholders": {}}, "reader6": "سعود الشريم", "@reader6": {"type": "text", "placeholders": {}}, "reader7": "سعد الغام<PERSON>ي", "@reader7": {"type": "text", "placeholders": {}}, "reader8": "مصطفى العزاوي", "@reader8": {"type": "text", "placeholders": {}}, "reader9": "ناصر القطامي", "@reader9": {"type": "text", "placeholders": {}}, "reminderToFastAshura": "تذكير بصيام عاشوراء", "@reminderToFastAshura": {"type": "text", "placeholders": {}}, "reminderToFastTasoo'a": "تذكير بصيام تاسوعاء", "@reminderToFastTasoo'a": {"type": "text", "placeholders": {}}, "reminders": "تذكير", "@reminders": {"type": "text", "placeholders": {}}, "repeatSurah": "<PERSON>yi tekrar et", "@repeatSurah": {"type": "text", "placeholders": {}}, "replaySurah": "Sureyi tekrar o<PERSON>t", "@replaySurah": {"type": "text", "placeholders": {}}, "reset": "إعادة ضبط", "@reset": {"type": "text", "placeholders": {}}, "retry": "<PERSON><PERSON><PERSON> dene", "@retry": {"type": "text", "placeholders": {}}, "sajda": "Secde", "@sajda": {"type": "text", "placeholders": {}}, "salat": "<PERSON><PERSON>", "@salat": {"type": "text", "placeholders": {}}, "save": "<PERSON><PERSON>", "@save": {"type": "text", "placeholders": {}}, "scholar_review": "<PERSON><PERSON>", "@scholar_review": {"type": "text", "placeholders": {}}, "scholar_validated": "Alimler tarafından doğrulandı", "@scholar_validated": {"type": "text", "placeholders": {}}, "search": "ب<PERSON><PERSON>", "@search": {"type": "text", "placeholders": {}}, "searchInBooks": "ابحث في الكتب", "@searchInBooks": {"type": "text", "placeholders": {}}, "searchToSurah": "إب<PERSON><PERSON> عن السورة", "@searchToSurah": {"type": "text", "placeholders": {}}, "search_description": "يمكنك البحث عن جميع آيات القرآن الكريم، فقط قم بكتابة كلمة من الآية.", "@search_description": {"type": "text", "placeholders": {}}, "search_hint": "Kur'an ayetlerinde ara", "@search_hint": {"description": "search_hint", "type": "text", "placeholders": {}}, "search_word": "Bir ayet ara", "@search_word": {"type": "text", "placeholders": {}}, "selectScreen": "Başlangıç ekranını seç", "@selectScreen": {"type": "text", "placeholders": {}}, "selectTime": "حدد وقتًا للتبينه", "@selectTime": {"type": "text", "placeholders": {}}, "select_player": "<PERSON><PERSON><PERSON><PERSON><PERSON> seç", "@select_player": {"type": "text", "placeholders": {}}, "setting": "<PERSON><PERSON><PERSON>", "@setting": {"type": "text", "placeholders": {}}, "sexShawwal": "تذكير بصيام الستّ من شوال", "@sexShawwal": {"type": "text", "placeholders": {}}, "share": "شارك التطبيق", "@share": {"type": "text", "placeholders": {}}, "shareImage": "كصورة", "@shareImage": {"type": "text", "placeholders": {}}, "shareImageWTrans": "كصورة مع محتوى إضافي", "@shareImageWTrans": {"type": "text", "placeholders": {}}, "shareTafseer": "Paylaş", "@shareTafseer": {"type": "text", "placeholders": {}}, "shareText": "كنص", "@shareText": {"type": "text", "placeholders": {}}, "shareTrans": "تنويه: مشاركة التفسير في صورة يدعم فقط تفسير السعدي، بسبب إن التفسير ليس مطول.", "@shareTrans": {"type": "text", "placeholders": {}}, "sign_in": "<PERSON><PERSON><PERSON>", "@sign_in": {"type": "text", "placeholders": {}}, "sign_in_sync_message": "Verilerinizi cihazlar arasında senkronize etmek için giriş yapın", "@sign_in_sync_message": {"type": "text", "placeholders": {}}, "sign_up": "<PERSON><PERSON><PERSON>", "@sign_up": {"type": "text", "placeholders": {}}, "skip": "تخطي", "@skip": {"type": "text", "placeholders": {}}, "skipToPrevious": "<PERSON><PERSON><PERSON>", "@skipToPrevious": {"type": "text", "placeholders": {}}, "sorah": "Sure", "@sorah": {"type": "text", "placeholders": {}}, "start": "<PERSON><PERSON><PERSON><PERSON>", "@start": {"type": "text", "placeholders": {}}, "startHijriYear": "بداية العام الهجري", "@startHijriYear": {"type": "text", "placeholders": {}}, "startScreen": "Başlangıç ekranı", "@startScreen": {"type": "text", "placeholders": {}}, "stopSigns": "<PERSON>rak işaretleri", "@stopSigns": {"type": "text", "placeholders": {}}, "stop_title": "Kur'an Vakfı - Hikmet <PERSON>ü<PERSON>ü<PERSON>nesi", "@stop_title": {"type": "text", "placeholders": {}}, "surahNames": "أسماء السورة", "@surahNames": {"type": "text", "placeholders": {}}, "surahsList": "قائمة السور", "@surahsList": {"type": "text", "placeholders": {}}, "sync_your_progress": "İlerlemenizi senkronize edin", "@sync_your_progress": {"type": "text", "placeholders": {}}, "tafBaghawyD": "معالم التنزيل", "@tafBaghawyD": {"type": "text", "placeholders": {}}, "tafBaghawyN": "Begavi <PERSON>", "@tafBaghawyN": {"type": "text", "placeholders": {}}, "tafChange": "<PERSON><PERSON><PERSON>", "@tafChange": {"type": "text", "placeholders": {}}, "tafIbnkatheerD": "تفسير القرآن العظيم", "@tafIbnkatheerD": {"type": "text", "placeholders": {}}, "tafIbnkatheerN": "İbn Kesir Tefsiri", "@tafIbnkatheerN": {"type": "text", "placeholders": {}}, "tafQurtubiD": "الجامع لأحكام القرآن", "@tafQurtubiD": {"type": "text", "placeholders": {}}, "tafQurtubiN": "<PERSON><PERSON>", "@tafQurtubiN": {"type": "text", "placeholders": {}}, "tafSaadiD": "تيسير الكريم الرحمن في تفسير كلام المنان", "@tafSaadiD": {"type": "text", "placeholders": {}}, "tafSaadiN": "<PERSON><PERSON><PERSON>", "@tafSaadiN": {"type": "text", "placeholders": {}}, "tafTabariD": "جامع البيان في تفسير القرآن", "@tafTabariD": {"type": "text", "placeholders": {}}, "tafTabariN": "Taberi <PERSON>", "@tafTabariN": {"type": "text", "placeholders": {}}, "tafseer": "التفسير", "@tafseer": {"type": "text", "placeholders": {}}, "tafsirLibrary": "مكتبة التفسير", "@tafsirLibrary": {"type": "text", "placeholders": {}}, "tenDaysOfDhul-Hijjah": "وليال عشر", "@tenDaysOfDhul-Hijjah": {"type": "text", "placeholders": {}}, "themeTitle": "Temayı seç", "@themeTitle": {"type": "text", "placeholders": {}}, "to": "إ<PERSON><PERSON>", "@to": {"type": "text", "placeholders": {}}, "translation": "الترجمة", "@translation": {"type": "text", "placeholders": {}}, "validate_by_scholars": "Alimler tarafından doğrula", "@validate_by_scholars": {"type": "text", "placeholders": {}}, "version": "الإصدار", "@version": {"type": "text", "placeholders": {}}, "waqfName": "<PERSON>rak işaretleri", "@waqfName": {"type": "text", "placeholders": {}}, "welcome_back": "<PERSON><PERSON><PERSON>", "@welcome_back": {"type": "text", "placeholders": {}}, "year": "سنة", "settings": "<PERSON><PERSON><PERSON>", "@settings": {"type": "text", "placeholders": {}}, "reading_settings": "Okuma Ayarları", "@reading_settings": {"type": "text", "placeholders": {}}, "font_size": "Yazı Boyutu", "@font_size": {"type": "text", "placeholders": {}}, "medium": "Orta", "@medium": {"type": "text", "placeholders": {}}, "font_used": "Kullanılan Yazı Tipi", "@font_used": {"type": "text", "placeholders": {}}, "hafs_smart_font": "Hafs Akıllı Yazı Tipi", "@hafs_smart_font": {"type": "text", "placeholders": {}}, "show_translation": "<PERSON><PERSON><PERSON><PERSON>", "@show_translation": {"type": "text", "placeholders": {}}, "enabled": "<PERSON><PERSON><PERSON>", "@enabled": {"type": "text", "placeholders": {}}, "ai_settings": "Yapay Zeka Ayarları", "@ai_settings": {"type": "text", "placeholders": {}}, "automatic_analysis": "Otomatik Analiz", "@automatic_analysis": {"type": "text", "placeholders": {}}, "detail_level": "Ayrıntı Düzeyi", "@detail_level": {"type": "text", "placeholders": {}}, "advanced": "Gelişmiş", "@advanced": {"type": "text", "placeholders": {}}, "reset_preferences": "Tercihleri Sıfırla", "@reset_preferences": {"type": "text", "placeholders": {}}, "restore_default_settings": "Varsayılan Ayarları Geri <PERSON>", "@restore_default_settings": {"type": "text", "placeholders": {}}, "app_settings": "Uygulama Ayarları", "@app_settings": {"type": "text", "placeholders": {}}, "dark_mode": "Karanlık Mod", "@dark_mode": {"type": "text", "placeholders": {}}, "disabled": "Devre Dışı", "@disabled": {"type": "text", "placeholders": {}}, "notifications": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@notifications": {"type": "text", "placeholders": {}}, "backup": "<PERSON><PERSON><PERSON><PERSON>", "@backup": {"type": "text", "placeholders": {}}, "automatic": "Otomatik", "@automatic": {"type": "text", "placeholders": {}}, "app_info": "Uygulama <PERSON>", "@app_info": {"type": "text", "placeholders": {}}, "developer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@developer": {"type": "text", "placeholders": {}}, "quranic_insights_team": "Kurani İ<PERSON>gör<PERSON><PERSON>", "@quranic_insights_team": {"type": "text", "placeholders": {}}, "support": "Destek", "@support": {"type": "text", "placeholders": {}}, "contact_us": "Bize Ulaşın", "@contact_us": {"type": "text", "placeholders": {}}, "admin_panel": "Yönetici Paneli", "@admin_panel": {"type": "text", "placeholders": {}}, "manage_ai_settings": "Yapay Zeka Ayarlarını Yönet", "@manage_ai_settings": {"type": "text", "placeholders": {}}, "control_models_prompts": "Modelleri ve istemleri kontrol et", "@control_models_prompts": {"type": "text", "placeholders": {}}, "choose_font_size": "Okuma için uygun yazı boyutunu seçin:", "@choose_font_size": {"type": "text", "placeholders": {}}, "small": "Küçük", "@small": {"type": "text", "placeholders": {}}, "large": "Büyük", "@large": {"type": "text", "placeholders": {}}, "very_large": "Çok Büyük", "@very_large": {"type": "text", "placeholders": {}}, "font_type": "Yazı Tipi Türü", "@font_type": {"type": "text", "placeholders": {}}, "font_settings_development": "Yazı tipi ayarları geliştiriliyor", "@font_settings_development": {"type": "text", "placeholders": {}}, "translation_settings": "<PERSON><PERSON><PERSON>", "@translation_settings": {"type": "text", "placeholders": {}}, "translation_settings_development": "Çeviri ayarları geliştiriliyor", "@translation_settings_development": {"type": "text", "placeholders": {}}, "ai_settings_development": "Yapay zeka ayarları geliştiriliyor", "@ai_settings_development": {"type": "text", "placeholders": {}}, "detail_level_settings_development": "Ayrıntı düzeyi ayarları geliştiriliyor", "@detail_level_settings_development": {"type": "text", "placeholders": {}}, "confirm_reset_preferences": "Varsayılan ayarları geri yüklemek istediğinizden emin misiniz?", "@confirm_reset_preferences": {"type": "text", "placeholders": {}}, "confirm": "<PERSON><PERSON><PERSON>", "@confirm": {"type": "text", "placeholders": {}}, "preferences_reset": "Terc<PERSON>ler sıfırlandı", "@preferences_reset": {"type": "text", "placeholders": {}}, "appearance_settings": "G<PERSON>r<PERSON><PERSON><PERSON><PERSON>", "@appearance_settings": {"type": "text", "placeholders": {}}, "appearance_settings_development": "G<PERSON>r<PERSON><PERSON><PERSON>m a<PERSON>ları geliştiriliyor", "@appearance_settings_development": {"type": "text", "placeholders": {}}, "notification_settings": "<PERSON><PERSON><PERSON><PERSON>", "@notification_settings": {"type": "text", "placeholders": {}}, "notification_settings_development": "Bil<PERSON>im a<PERSON>ları geliştiriliyor", "@notification_settings_development": {"type": "text", "placeholders": {}}, "backup_settings": "Yedekleme <PERSON>", "@backup_settings": {"type": "text", "placeholders": {}}, "backup_settings_development": "Yedekleme ayarları geliştiriliyor", "@backup_settings_development": {"type": "text", "placeholders": {}}, "quranic_insights": "<PERSON><PERSON>", "@quranic_insights": {"type": "text", "placeholders": {}}, "app_description": "Yapay zeka teknolojilerini kullanarak Kutsal Kuran ile akıllı etkileşim için bir uygulama", "@app_description": {"type": "text", "placeholders": {}}, "developer_info": "Geliştirici Bilgileri", "@developer_info": {"type": "text", "placeholders": {}}, "specializing_islamic_apps": "İslami uygulama geliştirme konusunda uzman", "@specializing_islamic_apps": {"type": "text", "placeholders": {}}, "support_message": "Destek için lütfen e-posta veya web sitemiz üzerinden bizimle iletişime geçin", "@support_message": {"type": "text", "placeholders": {}}}