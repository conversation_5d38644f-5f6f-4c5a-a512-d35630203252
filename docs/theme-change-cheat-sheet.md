# Theme Change Cheat Sheet - Quranic Insights App

## Quick Reference Guide

### 🎨 Current Theme System
- **4 Predefined Themes**: Blue, <PERSON>, <PERSON> (Green), Dark
- **Material 2 Design** (`useMaterial3: false`)
- **Theme Persistence**: Saved to local storage via GetStorage

### 📁 Key Files

| File | Purpose | Location |
|------|---------|----------|
| **app_themes.dart** | Theme definitions | `/lib/core/utils/helpers/app_themes.dart` |
| **theme_controller.dart** | Theme state management | `/lib/presentation/controllers/theme_controller.dart` |
| **lists.dart** | Theme list configuration | `/lib/core/utils/constants/lists.dart` |
| **theme SVGs** | Theme preview icons | `/assets/svg/theme0-3.svg` |

### 🎨 Color Properties to Define

Each theme requires these color definitions:

```dart
// Primary Colors
primary          // Main app color
primaryLight     // Lighter variant
primaryDark      // Darker variant

// Surface Colors  
surface          // Card/surface backgrounds
surfaceContainer // Container backgrounds
canvasColor      // Canvas/page background
scaffoldBackgroundColor // App background

// Accent Colors
secondary        // Accent color
dividerColor     // Dividers/borders
highlightColor   // Selection highlights

// Text Colors
onPrimary        // Text on primary color
onSurface        // Text on surfaces
hintColor        // Hint/placeholder text

// System Colors
error/onError    // Error states
focusColor       // Focus indicators
```

### 🔧 Quick Theme Modification

#### Option 1: Update Existing Theme Colors

```dart
// In app_themes.dart
final ThemeData blueTheme = ThemeData.light(
  useMaterial3: false,
).copyWith(
  colorScheme: const ColorScheme(
    primary: Color(0xffYOURCOLOR),  // Change this
    secondary: Color(0xffYOURCOLOR), // Change this
    // ... update other colors
  ),
  primaryColor: const Color(0xffYOURCOLOR), // Match primary above
  scaffoldBackgroundColor: const Color(0xffYOURCOLOR),
  // ... update matching colors
);
```

#### Option 2: Add a New Theme

1. **Define the theme** in `app_themes.dart`:
```dart
final ThemeData customTheme = ThemeData.light(
  useMaterial3: false,
).copyWith(
  colorScheme: const ColorScheme(
    // ... your colors
  ),
  // ... other properties
);
```

2. **Add to theme enum** in `theme_controller.dart`:
```dart
enum AppTheme {
  blue,
  brown,
  old,
  dark,
  custom  // Add your theme
}
```

3. **Update theme list** in `lists.dart`:
```dart
{
  'name': AppTheme.custom,
  'title': 'customMode',
  'svgUrl': 'assets/svg/theme4.svg',
}
```

4. **Add translation** for theme name in locale files

### 🎯 Color Selection Guidelines

#### For Quran Apps:
- **Primary**: Deep, respectful colors (navy, forest green, deep brown)
- **Background**: Light cream/beige or true black for OLED
- **Text**: High contrast (min 7:1 for AAA compliance)
- **Accent**: Gold, emerald green, or subtle earth tones

#### Recommended Color Palettes:

**Modern Islamic Green**
```dart
primary: Color(0xff1B5E20),      // Deep green
secondary: Color(0xffFFD700),    // Gold accent
surface: Color(0xffF5F5DC),      // Beige
```

**Royal Navy**
```dart
primary: Color(0xff1A237E),      // Deep navy
secondary: Color(0xffFFC107),    // Amber accent
surface: Color(0xffFAFAFA),      // Off-white
```

**Warm Earth**
```dart
primary: Color(0xff5D4037),      // Dark brown
secondary: Color(0xffFF6F00),    // Deep orange
surface: Color(0xffFFF8E1),      // Cream
```

**Modern Dark**
```dart
primary: Color(0xff121212),      // True black
secondary: Color(0xff00BFA5),    // Teal accent
surface: Color(0xff1E1E1E),      // Dark grey
```

### ⚡ Quick Commands

```bash
# Test theme changes
flutter run -d chrome --web-port 3000

# Hot reload after color changes
r (in terminal)

# Generate any required files
flutter pub run build_runner build --delete-conflicting-outputs
```

### ✅ Implementation Checklist

- [ ] Define color palette (use color picker: https://colorhunt.co)
- [ ] Update colors in `app_themes.dart`
- [ ] Test text readability on Quran pages
- [ ] Verify color contrast (https://webaim.org/resources/contrastchecker/)
- [ ] Create/update theme preview SVG (200x150px)
- [ ] Test RTL layout with Arabic text
- [ ] Test on different screen brightness levels
- [ ] Update any hardcoded colors in widgets
- [ ] Test all UI components (buttons, cards, dialogs)
- [ ] Verify dark mode if applicable

### 🚀 Pro Tips

1. **Use Color Variables**: Define colors once and reuse
```dart
const Color myPrimary = Color(0xff404C6E);
const Color mySecondary = Color(0xffCDAD80);
```

2. **Test with Real Content**: Always test with actual Quran text
3. **Consider Time of Day**: Some users read at night (dark theme important)
4. **Platform Consistency**: Test on Android, iOS, and Web
5. **Respect Tradition**: Avoid overly bright or playful colors

### 🔍 Where Colors Are Used

| Component | Uses These Colors |
|-----------|-------------------|
| **App Bar** | primary, onPrimary |
| **Quran Page** | canvasColor, primary (text) |
| **Cards** | cardColor, surface |
| **Buttons** | secondary, onSecondary |
| **Dividers** | dividerColor |
| **Selections** | highlightColor, focusColor |

### 🎨 Material 3 Migration (Future)

To upgrade to Material 3:
1. Change `useMaterial3: true`
2. Use `ColorScheme.fromSeed()` for dynamic colors
3. Update component themes
4. Test thoroughly as some properties change

### 🐛 Common Issues

**Issue**: Colors not updating
- **Solution**: Hot restart (Shift+R) instead of hot reload

**Issue**: Text unreadable
- **Solution**: Check contrast ratios, use `onPrimary`, `onSurface` correctly

**Issue**: SVG preview not showing
- **Solution**: Ensure SVG path is correct and file exists

### 📝 Example: Changing to Dark Navy Theme

```dart
// 1. In app_themes.dart, update blueTheme:
final ThemeData blueTheme = ThemeData.light(
  useMaterial3: false,
).copyWith(
  colorScheme: const ColorScheme(
    brightness: Brightness.light,
    primary: Color(0xff1C2841),      // Dark navy
    onPrimary: Color(0xffFFFFFF),    // White text
    secondary: Color(0xff3498DB),    // Bright blue accent
    surface: Color(0xffF8F9FA),      // Light grey surface
    onSurface: Color(0xff1C2841),    // Dark text on surface
    // ... other colors
  ),
  primaryColor: const Color(0xff1C2841),
  scaffoldBackgroundColor: const Color(0xffFFFFFF),
  // ... matching colors
);
```

### 🎯 Final Notes

- Always backup `app_themes.dart` before major changes
- Consider user preferences - some prefer high contrast
- Test with actual users from your target audience
- Document your color choices for team consistency

---

**Created**: December 2024
**App**: Quranic Insights
**Current Themes**: Blue, Brown, Old (Green), Dark