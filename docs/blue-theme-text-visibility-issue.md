# Blue Theme Text Visibility Issue Analysis

## Problem Summary
Text in the home screen widgets may not be visible when using the blue theme due to missing legacy theme color properties.

## Root Cause
The blue theme uses Material 3 (`useMaterial3: true`) but doesn't define legacy color properties that are still being used by widgets:
- `canvasColor`
- `hintColor` 
- `cardColor`
- `disabledColor`

## Affected Widgets and Their Color Usage

### 1. `hijri_widget.dart`
- Uses `Theme.of(context).canvasColor` for text (lines 68, 84, 93, 106)
- Uses `Theme.of(context).disabledColor` for progress text (lines 144, 162, 180)

### 2. `last_read.dart`
- Uses `Theme.of(context).hintColor` for page number text (line 110)
- Uses `Theme.of(context).cardColor` for SVG coloring (line 88)

### 3. `screens_list.dart`
- Uses `Theme.of(context).colorScheme.secondary` for text (lines 75-77)

### 4. `ayah_tafsir_widget.dart`
- Uses `Theme.of(context).colorScheme.inversePrimary` for main text (lines 43, 86, 103)
- Uses `Theme.of(context).hintColor` for button text (lines 116, 118)

### 5. `daily_zeker.dart`
- Uses `Theme.of(context).colorScheme.inversePrimary` for title (line 70)
- Uses `Theme.of(context).hintColor` for main text (lines 93, 111)

## Theme Comparison

### Blue Theme (Material 3)
- Primary: `Color(0xff404C6E)` (dark blue)
- onPrimary: `Color(0xffFFFFFF)` (white)
- inversePrimary: `Color(0xffADBCE9)` (light blue)
- **Missing**: canvasColor, hintColor, cardColor, disabledColor

### Brown Theme (Material 2)
- canvasColor: `Color(0xffF2E5D5)`
- hintColor: `Color(0xff000000)`
- cardColor: `Color(0xff583623)`
- disabledColor: `Color(0xff000000)`

### Old Theme (Material 2)
- canvasColor: `Color(0xfff3efdf)`
- hintColor: `Color(0xff232c13)`
- cardColor: `Color(0xff232c13)`
- disabledColor: `Color(0xff000000)`

## Solution Options

### Option 1: Add Legacy Properties to Blue Theme
Add the missing legacy color properties to the blue theme definition:
```dart
canvasColor: const Color(0xff1B1B1F), // Dark text on light background
hintColor: const Color(0xff5F6368), // Medium gray for secondary text
cardColor: const Color(0xffE8EAF6), // Light blue for cards
disabledColor: const Color(0xff9E9E9E), // Gray for disabled text
```

### Option 2: Update Widgets to Use Material 3 Colors
Replace legacy color usage in widgets with Material 3 semantic colors:
- Replace `canvasColor` with `colorScheme.onSurface` or `colorScheme.onPrimaryContainer`
- Replace `hintColor` with `colorScheme.onSurfaceVariant` or custom colors
- Replace `cardColor` with `colorScheme.surfaceContainerLow`
- Replace `disabledColor` with `colorScheme.onSurface.withValues(alpha: 0.38)`

### Option 3: Create Color Mapping Function
Create a helper function that maps legacy colors to Material 3 equivalents based on the theme's `useMaterial3` property.

## Recommended Solution
Option 2 is recommended as it aligns with Material 3 design principles and ensures consistency across the app. This involves updating the widgets to use semantic colors from the ColorScheme instead of legacy properties.