# Production Deployment Checklist

## Pre-Deployment Steps

### 1. Environment Setup
- [ ] Copy `.env.production.example` to `.env.production`
- [ ] Fill in all production API keys and secrets
- [ ] Verify all Supabase credentials are correct
- [ ] Set up production domain and SSL certificates

### 2. Database Preparation
- [ ] Run all migrations on production database
- [ ] Verify RLS policies are enabled on all tables
- [ ] Test rate limiting functions
- [ ] Create initial admin user
- [ ] Set up database backups

### 3. Security Verification
- [ ] Audit all environment variables for sensitive data
- [ ] Verify CORS settings are restrictive
- [ ] Test rate limiting for anonymous users
- [ ] Confirm audit logging is working
- [ ] Review and test all RLS policies

### 4. Code Review
- [ ] Remove all console.log/print statements
- [ ] Verify no hardcoded API keys or secrets
- [ ] Ensure error messages don't expose sensitive info
- [ ] Check for proper input sanitization
- [ ] Review authentication flows

## Deployment Process

### 1. Build Process
```bash
# Clean build directory
flutter clean

# Get dependencies
flutter pub get

# Generate code (Freezed, Drift, etc.)
flutter pub run build_runner build --delete-conflicting-outputs

# Build for production
flutter build web --release --dart-define-from-file=.env.production
flutter build apk --release --dart-define-from-file=.env.production
flutter build ios --release --dart-define-from-file=.env.production
```

### 2. Web Deployment
- [ ] Upload web build to hosting service
- [ ] Configure CDN for static assets
- [ ] Set up proper cache headers
- [ ] Configure security headers (CSP, HSTS, etc.)
- [ ] Test on multiple browsers

### 3. Mobile Deployment
- [ ] Submit to Google Play Store
- [ ] Submit to Apple App Store
- [ ] Configure push notification certificates
- [ ] Set up crash reporting (Sentry)

### 4. Backend Deployment
- [ ] Deploy Edge Functions to Supabase
- [ ] Configure storage buckets and policies
- [ ] Set up scheduled functions for cleanup
- [ ] Configure email service for notifications

## Post-Deployment Verification

### 1. Functional Testing
- [ ] Test anonymous user flow
- [ ] Test user registration and login
- [ ] Verify data sync between devices
- [ ] Test offline functionality
- [ ] Verify AI insights are working
- [ ] Test scholar review system
- [ ] Check Quran text corrections sync

### 2. Performance Testing
- [ ] Load test API endpoints
- [ ] Verify database query performance
- [ ] Check image/asset loading times
- [ ] Test app startup time
- [ ] Monitor memory usage

### 3. Security Testing
- [ ] Test rate limiting
- [ ] Verify authentication tokens expire properly
- [ ] Check for SQL injection vulnerabilities
- [ ] Test XSS protection
- [ ] Verify HTTPS is enforced

### 4. Monitoring Setup
- [ ] Configure error tracking (Sentry)
- [ ] Set up performance monitoring
- [ ] Configure uptime monitoring
- [ ] Set up database monitoring
- [ ] Create alerting rules

## Rollback Plan

### If Issues Occur:
1. Keep previous version accessible at `previous.quranicinsights.app`
2. Database rollback scripts ready in `/supabase/rollback/`
3. CDN cache can be purged if needed
4. Mobile apps can be rolled back via store consoles

### Emergency Contacts:
- DevOps Lead: [Contact Info]
- Database Admin: [Contact Info]
- Security Team: [Contact Info]

## Production URLs

- Web App: https://quranicinsights.app
- API: https://api.quranicinsights.app
- Admin Dashboard: https://admin.quranicinsights.app
- Status Page: https://status.quranicinsights.app

## Important Notes

1. **Anonymous Users**: The app supports anonymous users with full functionality. Ensure rate limiting is properly configured to prevent abuse.

2. **Data Migration**: When users convert from anonymous to authenticated, their data is automatically migrated. Test this flow thoroughly.

3. **AI Services**: Monitor AI API usage closely as costs can escalate quickly. Consider implementing usage quotas.

4. **Scholar Review**: The scholar review system requires admin approval for scholar accounts. Have a process in place for vetting scholars.

5. **Backup Strategy**: Daily automated backups are configured, but also perform manual backups before major updates.

## Sign-off

- [ ] Development Team Lead
- [ ] QA Lead  
- [ ] Security Officer
- [ ] DevOps Engineer
- [ ] Project Manager