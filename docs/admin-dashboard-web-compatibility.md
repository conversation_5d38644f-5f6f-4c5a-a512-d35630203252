# Admin Dashboard Web Compatibility

## Overview
This document describes the web compatibility enhancements made to the Admin Dashboard for the Quranic Insights app.

## Changes Made

### 1. Development Mode Enabled
- Enabled development mode in `AdminApiService` to use simulated data and avoid CORS issues during web testing
- Location: `lib/features/admin/services/admin_api_service.dart`
- Change: `_isDevelopmentMode = true`

### 2. Responsive Web Wrapper
- Created `WebResponsiveWrapper` widget for proper layout on web platforms
- Location: `lib/features/admin/widgets/web_responsive_wrapper.dart`
- Features:
  - Maximum width constraint (default: 1200px)
  - Centered content
  - Configurable padding

### 3. Admin Dashboard Screen Updates
- Added web-specific imports and responsive handling
- Refactored dashboard content into `_buildDashboardContent` method
- Applied responsive wrapper when running on web (`kIsWeb`)
- Location: `lib/features/admin/screens/admin_dashboard_screen.dart`

## How It Works

1. **Platform Detection**: Uses `kIsWeb` to detect when running on web
2. **Responsive Layout**: Applies `WebResponsiveWrapper` on web to constrain max width
3. **Development Mode**: Uses simulated data to avoid backend dependencies during testing
4. **Error Handling**: Falls back to mock data if API calls fail

## Testing

To test the admin dashboard on web:

1. Ensure Flutter web server is running: `flutter run -d web-server --web-port 3000`
2. Navigate to the home screen
3. Click on "Admin Panel" in the available screens list
4. The dashboard should load with mock data showing:
   - Models: 2 active
   - Prompts: 2 active  
   - Configurations: 1 active

## Next Steps

1. Test navigation to sub-screens (Models Management, Prompts Management, etc.)
2. Implement proper routing for web using go_router
3. Add more responsive breakpoints for different screen sizes
4. Disable development mode when backend is ready

## Notes

- The admin dashboard is accessible to all users currently (no authentication required)
- Development mode should be disabled before production deployment
- CORS headers need to be configured on Supabase Edge Functions for production