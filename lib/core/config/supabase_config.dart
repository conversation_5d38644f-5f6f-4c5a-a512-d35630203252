import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Supabase configuration for AI integration
class SupabaseConfig {
  final String supabaseUrl;
  final String supabaseAnonKey;
  final String supabaseServiceRoleKey;

  SupabaseConfig({
    required this.supabaseUrl,
    required this.supabaseAnonKey,
    required this.supabaseServiceRoleKey,
  });

  factory SupabaseConfig.fromEnv() => SupabaseConfig(
      supabaseUrl: dotenv.env['SUPABASE_URL'] ?? 'https://your-project-ref.supabase.co',
      supabaseAnonKey: dotenv.env['SUPABASE_ANON_KEY'] ?? 'your-anon-key-here',
      supabaseServiceRoleKey: dotenv.env['SUPABASE_SERVICE_ROLE_KEY'] ?? 'your-service-role-key-here',
    );

  // Edge Functions URLs
  String get openaiProxyUrl => '$supabaseUrl/functions/v1/openai-proxy';
  String get claudeProxyUrl => '$supabaseUrl/functions/v1/claude-proxy';
  String get aiRouterUrl => '$supabaseUrl/functions/v1/ai-router';
  String get insightCacheUrl => '$supabaseUrl/functions/v1/insight-cache';
  String get aiBatchUrl => '$supabaseUrl/functions/v1/ai-batch';
  String get aiConfigurationUrl => '$supabaseUrl/functions/v1/ai-configuration'; // Added for admin

  // Headers for API requests
  Map<String, String> get defaultHeaders => <String, String>{
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $supabaseAnonKey',
        'apikey': supabaseAnonKey,
      };

  Map<String, String> get serviceRoleHeaders => <String, String>{
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $supabaseServiceRoleKey',
        'apikey': supabaseServiceRoleKey,
      };

  // Configuration validation
  bool get isConfigured =>
      supabaseUrl != 'https://your-project-ref.supabase.co' &&
      supabaseUrl.isNotEmpty &&
      supabaseAnonKey != 'your-anon-key-here' &&
      supabaseAnonKey.isNotEmpty;

  bool get hasServiceRoleKey =>
      supabaseServiceRoleKey != 'your-service-role-key-here' &&
      supabaseServiceRoleKey.isNotEmpty;

  String get configurationStatus {
    if (!isConfigured) {
      return 'Supabase not configured. Check .env file for SUPABASE_URL and SUPABASE_ANON_KEY.';
    }
    if (!hasServiceRoleKey) {
      return 'Supabase partially configured. SUPABASE_SERVICE_ROLE_KEY missing in .env for advanced features.';
    }
    return 'Supabase fully configured and loaded from .env.';
  }
}

/// Riverpod provider for SupabaseConfig
/// This will load the configuration from .env when first read.
final Provider<SupabaseConfig> supabaseConfigProvider = Provider<SupabaseConfig>((ProviderRef<SupabaseConfig> ref) {
  // Assuming dotenv is loaded in main.dart or similar entry point
  // If not, `await dotenv.load();` might be needed here, but providers should be synchronous.
  // It's best practice to ensure .env is loaded before the app runs.
  return SupabaseConfig.fromEnv();
});
